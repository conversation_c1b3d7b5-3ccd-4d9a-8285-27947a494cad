{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/src/lib/users.ts"], "sourcesContent": ["import { User, CreateUserData, UpdateUserData, UserFilters, PaginationParams, UserListResponse } from '@/types/user';\n\n// 模拟用户数据存储\nlet users: User[] = [\n  {\n    id: '1',\n    name: '张三',\n    email: '<PERSON><PERSON><PERSON>@example.com',\n    phone: '13800138001',\n    role: 'admin',\n    status: 'active',\n    department: '技术部',\n    position: '高级工程师',\n    createdAt: '2024-01-15T08:00:00Z',\n    updatedAt: '2024-01-15T08:00:00Z',\n  },\n  {\n    id: '2',\n    name: '李四',\n    email: '<EMAIL>',\n    phone: '13800138002',\n    role: 'user',\n    status: 'active',\n    department: '产品部',\n    position: '产品经理',\n    createdAt: '2024-01-16T09:00:00Z',\n    updatedAt: '2024-01-16T09:00:00Z',\n  },\n  {\n    id: '3',\n    name: '王五',\n    email: '<EMAIL>',\n    phone: '13800138003',\n    role: 'moderator',\n    status: 'pending',\n    department: '运营部',\n    position: '运营专员',\n    createdAt: '2024-01-17T10:00:00Z',\n    updatedAt: '2024-01-17T10:00:00Z',\n  },\n];\n\n// 生成唯一ID\nfunction generateId(): string {\n  return Date.now().toString() + Math.random().toString(36).substr(2, 9);\n}\n\n// 获取用户列表\nexport function getUsers(filters?: UserFilters, pagination?: PaginationParams): UserListResponse {\n  let filteredUsers = [...users];\n\n  // 应用筛选\n  if (filters) {\n    if (filters.search) {\n      const searchLower = filters.search.toLowerCase();\n      filteredUsers = filteredUsers.filter(user =>\n        user.name.toLowerCase().includes(searchLower) ||\n        user.email.toLowerCase().includes(searchLower) ||\n        user.phone?.toLowerCase().includes(searchLower)\n      );\n    }\n\n    if (filters.role) {\n      filteredUsers = filteredUsers.filter(user => user.role === filters.role);\n    }\n\n    if (filters.status) {\n      filteredUsers = filteredUsers.filter(user => user.status === filters.status);\n    }\n\n    if (filters.department) {\n      filteredUsers = filteredUsers.filter(user => user.department === filters.department);\n    }\n  }\n\n  const total = filteredUsers.length;\n\n  // 应用分页\n  if (pagination) {\n    const startIndex = (pagination.page - 1) * pagination.limit;\n    const endIndex = startIndex + pagination.limit;\n    filteredUsers = filteredUsers.slice(startIndex, endIndex);\n  }\n\n  return {\n    users: filteredUsers,\n    total,\n    page: pagination?.page || 1,\n    limit: pagination?.limit || total,\n    totalPages: pagination ? Math.ceil(total / pagination.limit) : 1,\n  };\n}\n\n// 根据ID获取用户\nexport function getUserById(id: string): User | null {\n  return users.find(user => user.id === id) || null;\n}\n\n// 创建用户\nexport function createUser(userData: CreateUserData): User {\n  // 检查邮箱是否已存在\n  const existingUser = users.find(user => user.email === userData.email);\n  if (existingUser) {\n    throw new Error('邮箱已存在');\n  }\n\n  const now = new Date().toISOString();\n  const newUser: User = {\n    id: generateId(),\n    ...userData,\n    createdAt: now,\n    updatedAt: now,\n  };\n\n  users.push(newUser);\n  return newUser;\n}\n\n// 更新用户\nexport function updateUser(userData: UpdateUserData): User {\n  const userIndex = users.findIndex(user => user.id === userData.id);\n  if (userIndex === -1) {\n    throw new Error('用户不存在');\n  }\n\n  // 检查邮箱是否被其他用户使用\n  if (userData.email) {\n    const existingUser = users.find(user => user.email === userData.email && user.id !== userData.id);\n    if (existingUser) {\n      throw new Error('邮箱已被其他用户使用');\n    }\n  }\n\n  const updatedUser: User = {\n    ...users[userIndex],\n    ...userData,\n    updatedAt: new Date().toISOString(),\n  };\n\n  users[userIndex] = updatedUser;\n  return updatedUser;\n}\n\n// 删除用户\nexport function deleteUser(id: string): boolean {\n  const userIndex = users.findIndex(user => user.id === id);\n  if (userIndex === -1) {\n    return false;\n  }\n\n  users.splice(userIndex, 1);\n  return true;\n}\n\n// 获取统计信息\nexport function getUserStats() {\n  const total = users.length;\n  const active = users.filter(user => user.status === 'active').length;\n  const inactive = users.filter(user => user.status === 'inactive').length;\n  const pending = users.filter(user => user.status === 'pending').length;\n\n  const roleStats = {\n    admin: users.filter(user => user.role === 'admin').length,\n    user: users.filter(user => user.role === 'user').length,\n    moderator: users.filter(user => user.role === 'moderator').length,\n  };\n\n  return {\n    total,\n    active,\n    inactive,\n    pending,\n    roleStats,\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA,WAAW;AACX,IAAI,QAAgB;IAClB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,UAAU;QACV,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,UAAU;QACV,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,UAAU;QACV,WAAW;QACX,WAAW;IACb;CACD;AAED,SAAS;AACT,SAAS;IACP,OAAO,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AACtE;AAGO,SAAS,SAAS,OAAqB,EAAE,UAA6B;IAC3E,IAAI,gBAAgB;WAAI;KAAM;IAE9B,OAAO;IACP,IAAI,SAAS;QACX,IAAI,QAAQ,MAAM,EAAE;YAClB,MAAM,cAAc,QAAQ,MAAM,CAAC,WAAW;YAC9C,gBAAgB,cAAc,MAAM,CAAC,CAAA,OACnC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACjC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAClC,KAAK,KAAK,EAAE,cAAc,SAAS;QAEvC;QAEA,IAAI,QAAQ,IAAI,EAAE;YAChB,gBAAgB,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,QAAQ,IAAI;QACzE;QAEA,IAAI,QAAQ,MAAM,EAAE;YAClB,gBAAgB,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,QAAQ,MAAM;QAC7E;QAEA,IAAI,QAAQ,UAAU,EAAE;YACtB,gBAAgB,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,UAAU,KAAK,QAAQ,UAAU;QACrF;IACF;IAEA,MAAM,QAAQ,cAAc,MAAM;IAElC,OAAO;IACP,IAAI,YAAY;QACd,MAAM,aAAa,CAAC,WAAW,IAAI,GAAG,CAAC,IAAI,WAAW,KAAK;QAC3D,MAAM,WAAW,aAAa,WAAW,KAAK;QAC9C,gBAAgB,cAAc,KAAK,CAAC,YAAY;IAClD;IAEA,OAAO;QACL,OAAO;QACP;QACA,MAAM,YAAY,QAAQ;QAC1B,OAAO,YAAY,SAAS;QAC5B,YAAY,aAAa,KAAK,IAAI,CAAC,QAAQ,WAAW,KAAK,IAAI;IACjE;AACF;AAGO,SAAS,YAAY,EAAU;IACpC,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO;AAC/C;AAGO,SAAS,WAAW,QAAwB;IACjD,YAAY;IACZ,MAAM,eAAe,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK,SAAS,KAAK;IACrE,IAAI,cAAc;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,MAAM,IAAI,OAAO,WAAW;IAClC,MAAM,UAAgB;QACpB,IAAI;QACJ,GAAG,QAAQ;QACX,WAAW;QACX,WAAW;IACb;IAEA,MAAM,IAAI,CAAC;IACX,OAAO;AACT;AAGO,SAAS,WAAW,QAAwB;IACjD,MAAM,YAAY,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,SAAS,EAAE;IACjE,IAAI,cAAc,CAAC,GAAG;QACpB,MAAM,IAAI,MAAM;IAClB;IAEA,gBAAgB;IAChB,IAAI,SAAS,KAAK,EAAE;QAClB,MAAM,eAAe,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK,SAAS,KAAK,IAAI,KAAK,EAAE,KAAK,SAAS,EAAE;QAChG,IAAI,cAAc;YAChB,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAM,cAAoB;QACxB,GAAG,KAAK,CAAC,UAAU;QACnB,GAAG,QAAQ;QACX,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,KAAK,CAAC,UAAU,GAAG;IACnB,OAAO;AACT;AAGO,SAAS,WAAW,EAAU;IACnC,MAAM,YAAY,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IACtD,IAAI,cAAc,CAAC,GAAG;QACpB,OAAO;IACT;IAEA,MAAM,MAAM,CAAC,WAAW;IACxB,OAAO;AACT;AAGO,SAAS;IACd,MAAM,QAAQ,MAAM,MAAM;IAC1B,MAAM,SAAS,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,UAAU,MAAM;IACpE,MAAM,WAAW,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,YAAY,MAAM;IACxE,MAAM,UAAU,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,WAAW,MAAM;IAEtE,MAAM,YAAY;QAChB,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,SAAS,MAAM;QACzD,MAAM,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,QAAQ,MAAM;QACvD,WAAW,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,aAAa,MAAM;IACnE;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/src/app/users/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useSearchParams } from 'next/navigation';\nimport Link from 'next/link';\nimport { User, UserFilters, PaginationParams } from '@/types/user';\nimport { getUsers, deleteUser } from '@/lib/users';\n\nexport default function UsersPage() {\n  const searchParams = useSearchParams();\n  const [users, setUsers] = useState<User[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [total, setTotal] = useState(0);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [successMessage, setSuccessMessage] = useState<string | null>(null);\n  const [filters, setFilters] = useState<UserFilters>({\n    search: '',\n    role: '',\n    status: '',\n    department: '',\n  });\n\n  const limit = 10;\n\n  // 检查是否有成功消息\n  useEffect(() => {\n    const success = searchParams.get('success');\n    if (success === 'created') {\n      setSuccessMessage('用户创建成功！');\n      setTimeout(() => setSuccessMessage(null), 3000);\n    } else if (success === 'updated') {\n      setSuccessMessage('用户更新成功！');\n      setTimeout(() => setSuccessMessage(null), 3000);\n    } else if (success === 'deleted') {\n      setSuccessMessage('用户删除成功！');\n      setTimeout(() => setSuccessMessage(null), 3000);\n    }\n  }, [searchParams]);\n\n  // 加载用户数据\n  const loadUsers = () => {\n    setLoading(true);\n    try {\n      const pagination: PaginationParams = {\n        page: currentPage,\n        limit,\n      };\n\n      const result = getUsers(filters, pagination);\n      setUsers(result.users);\n      setTotal(result.total);\n      setTotalPages(result.totalPages);\n    } catch (error) {\n      console.error('加载用户失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    loadUsers();\n  }, [currentPage, filters]);\n\n  // 处理筛选变化\n  const handleFilterChange = (key: keyof UserFilters, value: string) => {\n    setFilters(prev => ({\n      ...prev,\n      [key]: value,\n    }));\n    setCurrentPage(1); // 重置到第一页\n  };\n\n  // 处理删除用户\n  const handleDeleteUser = (userId: string, userName: string) => {\n    if (window.confirm(`确定要删除用户 \"${userName}\" 吗？此操作不可撤销。`)) {\n      try {\n        deleteUser(userId);\n        loadUsers(); // 重新加载数据\n        setSuccessMessage('用户删除成功！');\n        setTimeout(() => setSuccessMessage(null), 3000);\n      } catch (error) {\n        alert('删除用户失败');\n      }\n    }\n  };\n\n  // 获取状态显示样式\n  const getStatusBadge = (status: string) => {\n    const styles = {\n      active: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',\n      inactive: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',\n      pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',\n    };\n    \n    const labels = {\n      active: '激活',\n      inactive: '未激活',\n      pending: '待审核',\n    };\n\n    return (\n      <span className={`px-2 py-1 text-xs font-medium rounded-full ${styles[status as keyof typeof styles]}`}>\n        {labels[status as keyof typeof labels]}\n      </span>\n    );\n  };\n\n  // 获取角色显示样式\n  const getRoleBadge = (role: string) => {\n    const styles = {\n      admin: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',\n      moderator: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',\n      user: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',\n    };\n    \n    const labels = {\n      admin: '超级管理员',\n      moderator: '管理员',\n      user: '普通用户',\n    };\n\n    return (\n      <span className={`px-2 py-1 text-xs font-medium rounded-full ${styles[role as keyof typeof styles]}`}>\n        {labels[role as keyof typeof labels]}\n      </span>\n    );\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* 页面标题和操作 */}\n        <div className=\"mb-8\">\n          <nav className=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-4\">\n            <Link href=\"/\" className=\"hover:text-gray-700 dark:hover:text-gray-200\">\n              首页\n            </Link>\n            <span>/</span>\n            <span className=\"text-gray-900 dark:text-white\">用户管理</span>\n          </nav>\n          \n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n                用户管理\n              </h1>\n              <p className=\"text-gray-600 dark:text-gray-300 mt-2\">\n                共 {total} 个用户\n              </p>\n            </div>\n            <Link\n              href=\"/users/add\"\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center space-x-2\"\n            >\n              <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n              </svg>\n              <span>添加用户</span>\n            </Link>\n          </div>\n        </div>\n\n        {/* 成功消息 */}\n        {successMessage && (\n          <div className=\"mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md\">\n            <p className=\"text-green-600 dark:text-green-400\">{successMessage}</p>\n          </div>\n        )}\n\n        {/* 筛选器 */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                搜索\n              </label>\n              <input\n                type=\"text\"\n                value={filters.search || ''}\n                onChange={(e) => handleFilterChange('search', e.target.value)}\n                placeholder=\"搜索姓名、邮箱或手机号\"\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                角色\n              </label>\n              <select\n                value={filters.role || ''}\n                onChange={(e) => handleFilterChange('role', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n              >\n                <option value=\"\">全部角色</option>\n                <option value=\"admin\">超级管理员</option>\n                <option value=\"moderator\">管理员</option>\n                <option value=\"user\">普通用户</option>\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                状态\n              </label>\n              <select\n                value={filters.status || ''}\n                onChange={(e) => handleFilterChange('status', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n              >\n                <option value=\"\">全部状态</option>\n                <option value=\"active\">激活</option>\n                <option value=\"inactive\">未激活</option>\n                <option value=\"pending\">待审核</option>\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                部门\n              </label>\n              <input\n                type=\"text\"\n                value={filters.department || ''}\n                onChange={(e) => handleFilterChange('department', e.target.value)}\n                placeholder=\"筛选部门\"\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n              />\n            </div>\n          </div>\n        </div>\n\n        {/* 用户列表 */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden\">\n          {loading ? (\n            <div className=\"p-8 text-center\">\n              <div className=\"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n              <p className=\"mt-2 text-gray-600 dark:text-gray-300\">加载中...</p>\n            </div>\n          ) : users.length === 0 ? (\n            <div className=\"p-8 text-center\">\n              <p className=\"text-gray-500 dark:text-gray-400\">没有找到用户</p>\n            </div>\n          ) : (\n            <>\n              <div className=\"overflow-x-auto\">\n                <table className=\"w-full\">\n                  <thead className=\"bg-gray-50 dark:bg-gray-700\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                        用户信息\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                        角色\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                        状态\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                        部门/职位\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                        创建时间\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                        操作\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n                    {users.map((user) => (\n                      <tr key={user.id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div>\n                            <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                              {user.name}\n                            </div>\n                            <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                              {user.email}\n                            </div>\n                            {user.phone && (\n                              <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                                {user.phone}\n                              </div>\n                            )}\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          {getRoleBadge(user.role)}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          {getStatusBadge(user.status)}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div className=\"text-sm text-gray-900 dark:text-white\">\n                            {user.department || '-'}\n                          </div>\n                          <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                            {user.position || '-'}\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\n                          {new Date(user.createdAt).toLocaleDateString('zh-CN')}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                          <div className=\"flex items-center justify-end space-x-2\">\n                            <Link\n                              href={`/users/${user.id}`}\n                              className=\"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300\"\n                            >\n                              查看\n                            </Link>\n                            <Link\n                              href={`/users/${user.id}/edit`}\n                              className=\"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300\"\n                            >\n                              编辑\n                            </Link>\n                            <button\n                              onClick={() => handleDeleteUser(user.id, user.name)}\n                              className=\"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300\"\n                            >\n                              删除\n                            </button>\n                          </div>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n\n              {/* 分页 */}\n              {totalPages > 1 && (\n                <div className=\"px-6 py-3 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"text-sm text-gray-700 dark:text-gray-300\">\n                      显示第 {(currentPage - 1) * limit + 1} 到 {Math.min(currentPage * limit, total)} 条，共 {total} 条记录\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                      <button\n                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}\n                        disabled={currentPage === 1}\n                        className=\"px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-100 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed\"\n                      >\n                        上一页\n                      </button>\n                      <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                        第 {currentPage} 页，共 {totalPages} 页\n                      </span>\n                      <button\n                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}\n                        disabled={currentPage === totalPages}\n                        className=\"px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-100 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed\"\n                      >\n                        下一页\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQe,SAAS;IACtB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;QAClD,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,YAAY;IACd;IAEA,MAAM,QAAQ;IAEd,YAAY;IACZ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAAU,aAAa,GAAG,CAAC;QACjC,IAAI,YAAY,WAAW;YACzB,kBAAkB;YAClB,WAAW,IAAM,kBAAkB,OAAO;QAC5C,OAAO,IAAI,YAAY,WAAW;YAChC,kBAAkB;YAClB,WAAW,IAAM,kBAAkB,OAAO;QAC5C,OAAO,IAAI,YAAY,WAAW;YAChC,kBAAkB;YAClB,WAAW,IAAM,kBAAkB,OAAO;QAC5C;IACF,GAAG;QAAC;KAAa;IAEjB,SAAS;IACT,MAAM,YAAY;QAChB,WAAW;QACX,IAAI;YACF,MAAM,aAA+B;gBACnC,MAAM;gBACN;YACF;YAEA,MAAM,SAAS,CAAA,GAAA,mHAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;YACjC,SAAS,OAAO,KAAK;YACrB,SAAS,OAAO,KAAK;YACrB,cAAc,OAAO,UAAU;QACjC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAa;KAAQ;IAEzB,SAAS;IACT,MAAM,qBAAqB,CAAC,KAAwB;QAClD,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,IAAI,EAAE;YACT,CAAC;QACD,eAAe,IAAI,SAAS;IAC9B;IAEA,SAAS;IACT,MAAM,mBAAmB,CAAC,QAAgB;QACxC,IAAI,OAAO,OAAO,CAAC,CAAC,SAAS,EAAE,SAAS,YAAY,CAAC,GAAG;YACtD,IAAI;gBACF,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE;gBACX,aAAa,SAAS;gBACtB,kBAAkB;gBAClB,WAAW,IAAM,kBAAkB,OAAO;YAC5C,EAAE,OAAO,OAAO;gBACd,MAAM;YACR;QACF;IACF;IAEA,WAAW;IACX,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAS;YACb,QAAQ;YACR,UAAU;YACV,SAAS;QACX;QAEA,MAAM,SAAS;YACb,QAAQ;YACR,UAAU;YACV,SAAS;QACX;QAEA,qBACE,8OAAC;YAAK,WAAW,CAAC,2CAA2C,EAAE,MAAM,CAAC,OAA8B,EAAE;sBACnG,MAAM,CAAC,OAA8B;;;;;;IAG5C;IAEA,WAAW;IACX,MAAM,eAAe,CAAC;QACpB,MAAM,SAAS;YACb,OAAO;YACP,WAAW;YACX,MAAM;QACR;QAEA,MAAM,SAAS;YACb,OAAO;YACP,WAAW;YACX,MAAM;QACR;QAEA,qBACE,8OAAC;YAAK,WAAW,CAAC,2CAA2C,EAAE,MAAM,CAAC,KAA4B,EAAE;sBACjG,MAAM,CAAC,KAA4B;;;;;;IAG1C;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAA+C;;;;;;8CAGxE,8OAAC;8CAAK;;;;;;8CACN,8OAAC;oCAAK,WAAU;8CAAgC;;;;;;;;;;;;sCAGlD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAmD;;;;;;sDAGjE,8OAAC;4CAAE,WAAU;;gDAAwC;gDAChD;gDAAM;;;;;;;;;;;;;8CAGb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;sDAEvE,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;gBAMX,gCACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAsC;;;;;;;;;;;8BAKvD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,MAAK;wCACL,OAAO,QAAQ,MAAM,IAAI;wCACzB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;wCAC5D,aAAY;wCACZ,WAAU;;;;;;;;;;;;0CAId,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,OAAO,QAAQ,IAAI,IAAI;wCACvB,UAAU,CAAC,IAAM,mBAAmB,QAAQ,EAAE,MAAM,CAAC,KAAK;wCAC1D,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAG;;;;;;0DACjB,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,8OAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,8OAAC;gDAAO,OAAM;0DAAO;;;;;;;;;;;;;;;;;;0CAIzB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,OAAO,QAAQ,MAAM,IAAI;wCACzB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;wCAC5D,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAG;;;;;;0DACjB,8OAAC;gDAAO,OAAM;0DAAS;;;;;;0DACvB,8OAAC;gDAAO,OAAM;0DAAW;;;;;;0DACzB,8OAAC;gDAAO,OAAM;0DAAU;;;;;;;;;;;;;;;;;;0CAI5B,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,MAAK;wCACL,OAAO,QAAQ,UAAU,IAAI;wCAC7B,UAAU,CAAC,IAAM,mBAAmB,cAAc,EAAE,MAAM,CAAC,KAAK;wCAChE,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAOlB,8OAAC;oBAAI,WAAU;8BACZ,wBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAAwC;;;;;;;;;;;+BAErD,MAAM,MAAM,KAAK,kBACnB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;6CAGlD;;0CACE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CAAM,WAAU;sDACf,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAoG;;;;;;kEAGlH,8OAAC;wDAAG,WAAU;kEAAoG;;;;;;kEAGlH,8OAAC;wDAAG,WAAU;kEAAoG;;;;;;kEAGlH,8OAAC;wDAAG,WAAU;kEAAoG;;;;;;kEAGlH,8OAAC;wDAAG,WAAU;kEAAoG;;;;;;kEAGlH,8OAAC;wDAAG,WAAU;kEAAqG;;;;;;;;;;;;;;;;;sDAKvH,8OAAC;4CAAM,WAAU;sDACd,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;oDAAiB,WAAU;;sEAC1B,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;;kFACC,8OAAC;wEAAI,WAAU;kFACZ,KAAK,IAAI;;;;;;kFAEZ,8OAAC;wEAAI,WAAU;kFACZ,KAAK,KAAK;;;;;;oEAEZ,KAAK,KAAK,kBACT,8OAAC;wEAAI,WAAU;kFACZ,KAAK,KAAK;;;;;;;;;;;;;;;;;sEAKnB,8OAAC;4DAAG,WAAU;sEACX,aAAa,KAAK,IAAI;;;;;;sEAEzB,8OAAC;4DAAG,WAAU;sEACX,eAAe,KAAK,MAAM;;;;;;sEAE7B,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAI,WAAU;8EACZ,KAAK,UAAU,IAAI;;;;;;8EAEtB,8OAAC;oEAAI,WAAU;8EACZ,KAAK,QAAQ,IAAI;;;;;;;;;;;;sEAGtB,8OAAC;4DAAG,WAAU;sEACX,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB,CAAC;;;;;;sEAE/C,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,4JAAA,CAAA,UAAI;wEACH,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;wEACzB,WAAU;kFACX;;;;;;kFAGD,8OAAC,4JAAA,CAAA,UAAI;wEACH,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;wEAC9B,WAAU;kFACX;;;;;;kFAGD,8OAAC;wEACC,SAAS,IAAM,iBAAiB,KAAK,EAAE,EAAE,KAAK,IAAI;wEAClD,WAAU;kFACX;;;;;;;;;;;;;;;;;;mDAlDE,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;4BA8DvB,aAAa,mBACZ,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;gDAA2C;gDACnD,CAAC,cAAc,CAAC,IAAI,QAAQ;gDAAE;gDAAI,KAAK,GAAG,CAAC,cAAc,OAAO;gDAAO;gDAAM;gDAAM;;;;;;;sDAE1F,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,OAAO,GAAG;oDACzD,UAAU,gBAAgB;oDAC1B,WAAU;8DACX;;;;;;8DAGD,8OAAC;oDAAK,WAAU;;wDAA2C;wDACtD;wDAAY;wDAAM;wDAAW;;;;;;;8DAElC,8OAAC;oDACC,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,OAAO,GAAG;oDACzD,UAAU,gBAAgB;oDAC1B,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAavB", "debugId": null}}]}
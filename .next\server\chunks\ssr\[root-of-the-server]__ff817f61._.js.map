{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/src/lib/users.ts"], "sourcesContent": ["import { User, CreateUserData, UpdateUserData, UserFilters, PaginationParams, UserListResponse } from '@/types/user';\n\n// 模拟用户数据存储\nlet users: User[] = [\n  {\n    id: '1',\n    name: '张三',\n    email: '<PERSON><PERSON><PERSON>@example.com',\n    phone: '13800138001',\n    role: 'admin',\n    status: 'active',\n    department: '技术部',\n    position: '高级工程师',\n    createdAt: '2024-01-15T08:00:00Z',\n    updatedAt: '2024-01-15T08:00:00Z',\n  },\n  {\n    id: '2',\n    name: '李四',\n    email: '<EMAIL>',\n    phone: '13800138002',\n    role: 'user',\n    status: 'active',\n    department: '产品部',\n    position: '产品经理',\n    createdAt: '2024-01-16T09:00:00Z',\n    updatedAt: '2024-01-16T09:00:00Z',\n  },\n  {\n    id: '3',\n    name: '王五',\n    email: '<EMAIL>',\n    phone: '13800138003',\n    role: 'moderator',\n    status: 'pending',\n    department: '运营部',\n    position: '运营专员',\n    createdAt: '2024-01-17T10:00:00Z',\n    updatedAt: '2024-01-17T10:00:00Z',\n  },\n];\n\n// 生成唯一ID\nfunction generateId(): string {\n  return Date.now().toString() + Math.random().toString(36).substr(2, 9);\n}\n\n// 获取用户列表\nexport function getUsers(filters?: UserFilters, pagination?: PaginationParams): UserListResponse {\n  let filteredUsers = [...users];\n\n  // 应用筛选\n  if (filters) {\n    if (filters.search) {\n      const searchLower = filters.search.toLowerCase();\n      filteredUsers = filteredUsers.filter(user =>\n        user.name.toLowerCase().includes(searchLower) ||\n        user.email.toLowerCase().includes(searchLower) ||\n        user.phone?.toLowerCase().includes(searchLower)\n      );\n    }\n\n    if (filters.role) {\n      filteredUsers = filteredUsers.filter(user => user.role === filters.role);\n    }\n\n    if (filters.status) {\n      filteredUsers = filteredUsers.filter(user => user.status === filters.status);\n    }\n\n    if (filters.department) {\n      filteredUsers = filteredUsers.filter(user => user.department === filters.department);\n    }\n  }\n\n  const total = filteredUsers.length;\n\n  // 应用分页\n  if (pagination) {\n    const startIndex = (pagination.page - 1) * pagination.limit;\n    const endIndex = startIndex + pagination.limit;\n    filteredUsers = filteredUsers.slice(startIndex, endIndex);\n  }\n\n  return {\n    users: filteredUsers,\n    total,\n    page: pagination?.page || 1,\n    limit: pagination?.limit || total,\n    totalPages: pagination ? Math.ceil(total / pagination.limit) : 1,\n  };\n}\n\n// 根据ID获取用户\nexport function getUserById(id: string): User | null {\n  return users.find(user => user.id === id) || null;\n}\n\n// 创建用户\nexport function createUser(userData: CreateUserData): User {\n  // 检查邮箱是否已存在\n  const existingUser = users.find(user => user.email === userData.email);\n  if (existingUser) {\n    throw new Error('邮箱已存在');\n  }\n\n  const now = new Date().toISOString();\n  const newUser: User = {\n    id: generateId(),\n    ...userData,\n    createdAt: now,\n    updatedAt: now,\n  };\n\n  users.push(newUser);\n  return newUser;\n}\n\n// 更新用户\nexport function updateUser(userData: UpdateUserData): User {\n  const userIndex = users.findIndex(user => user.id === userData.id);\n  if (userIndex === -1) {\n    throw new Error('用户不存在');\n  }\n\n  // 检查邮箱是否被其他用户使用\n  if (userData.email) {\n    const existingUser = users.find(user => user.email === userData.email && user.id !== userData.id);\n    if (existingUser) {\n      throw new Error('邮箱已被其他用户使用');\n    }\n  }\n\n  const updatedUser: User = {\n    ...users[userIndex],\n    ...userData,\n    updatedAt: new Date().toISOString(),\n  };\n\n  users[userIndex] = updatedUser;\n  return updatedUser;\n}\n\n// 删除用户\nexport function deleteUser(id: string): boolean {\n  const userIndex = users.findIndex(user => user.id === id);\n  if (userIndex === -1) {\n    return false;\n  }\n\n  users.splice(userIndex, 1);\n  return true;\n}\n\n// 获取统计信息\nexport function getUserStats() {\n  const total = users.length;\n  const active = users.filter(user => user.status === 'active').length;\n  const inactive = users.filter(user => user.status === 'inactive').length;\n  const pending = users.filter(user => user.status === 'pending').length;\n\n  const roleStats = {\n    admin: users.filter(user => user.role === 'admin').length,\n    user: users.filter(user => user.role === 'user').length,\n    moderator: users.filter(user => user.role === 'moderator').length,\n  };\n\n  return {\n    total,\n    active,\n    inactive,\n    pending,\n    roleStats,\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA,WAAW;AACX,IAAI,QAAgB;IAClB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,UAAU;QACV,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,UAAU;QACV,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,UAAU;QACV,WAAW;QACX,WAAW;IACb;CACD;AAED,SAAS;AACT,SAAS;IACP,OAAO,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AACtE;AAGO,SAAS,SAAS,OAAqB,EAAE,UAA6B;IAC3E,IAAI,gBAAgB;WAAI;KAAM;IAE9B,OAAO;IACP,IAAI,SAAS;QACX,IAAI,QAAQ,MAAM,EAAE;YAClB,MAAM,cAAc,QAAQ,MAAM,CAAC,WAAW;YAC9C,gBAAgB,cAAc,MAAM,CAAC,CAAA,OACnC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACjC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAClC,KAAK,KAAK,EAAE,cAAc,SAAS;QAEvC;QAEA,IAAI,QAAQ,IAAI,EAAE;YAChB,gBAAgB,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,QAAQ,IAAI;QACzE;QAEA,IAAI,QAAQ,MAAM,EAAE;YAClB,gBAAgB,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,QAAQ,MAAM;QAC7E;QAEA,IAAI,QAAQ,UAAU,EAAE;YACtB,gBAAgB,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,UAAU,KAAK,QAAQ,UAAU;QACrF;IACF;IAEA,MAAM,QAAQ,cAAc,MAAM;IAElC,OAAO;IACP,IAAI,YAAY;QACd,MAAM,aAAa,CAAC,WAAW,IAAI,GAAG,CAAC,IAAI,WAAW,KAAK;QAC3D,MAAM,WAAW,aAAa,WAAW,KAAK;QAC9C,gBAAgB,cAAc,KAAK,CAAC,YAAY;IAClD;IAEA,OAAO;QACL,OAAO;QACP;QACA,MAAM,YAAY,QAAQ;QAC1B,OAAO,YAAY,SAAS;QAC5B,YAAY,aAAa,KAAK,IAAI,CAAC,QAAQ,WAAW,KAAK,IAAI;IACjE;AACF;AAGO,SAAS,YAAY,EAAU;IACpC,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO;AAC/C;AAGO,SAAS,WAAW,QAAwB;IACjD,YAAY;IACZ,MAAM,eAAe,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK,SAAS,KAAK;IACrE,IAAI,cAAc;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,MAAM,IAAI,OAAO,WAAW;IAClC,MAAM,UAAgB;QACpB,IAAI;QACJ,GAAG,QAAQ;QACX,WAAW;QACX,WAAW;IACb;IAEA,MAAM,IAAI,CAAC;IACX,OAAO;AACT;AAGO,SAAS,WAAW,QAAwB;IACjD,MAAM,YAAY,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,SAAS,EAAE;IACjE,IAAI,cAAc,CAAC,GAAG;QACpB,MAAM,IAAI,MAAM;IAClB;IAEA,gBAAgB;IAChB,IAAI,SAAS,KAAK,EAAE;QAClB,MAAM,eAAe,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK,SAAS,KAAK,IAAI,KAAK,EAAE,KAAK,SAAS,EAAE;QAChG,IAAI,cAAc;YAChB,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAM,cAAoB;QACxB,GAAG,KAAK,CAAC,UAAU;QACnB,GAAG,QAAQ;QACX,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,KAAK,CAAC,UAAU,GAAG;IACnB,OAAO;AACT;AAGO,SAAS,WAAW,EAAU;IACnC,MAAM,YAAY,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IACtD,IAAI,cAAc,CAAC,GAAG;QACpB,OAAO;IACT;IAEA,MAAM,MAAM,CAAC,WAAW;IACxB,OAAO;AACT;AAGO,SAAS;IACd,MAAM,QAAQ,MAAM,MAAM;IAC1B,MAAM,SAAS,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,UAAU,MAAM;IACpE,MAAM,WAAW,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,YAAY,MAAM;IACxE,MAAM,UAAU,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,WAAW,MAAM;IAEtE,MAAM,YAAY;QAChB,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,SAAS,MAAM;QACzD,MAAM,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,QAAQ,MAAM;QACvD,WAAW,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,aAAa,MAAM;IACnE;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/src/app/users/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useParams, useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { User } from '@/types/user';\nimport { getUserById, deleteUser } from '@/lib/users';\n\nexport default function UserDetailPage() {\n  const params = useParams();\n  const router = useRouter();\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const userId = params.id as string;\n\n  useEffect(() => {\n    if (userId) {\n      try {\n        const userData = getUserById(userId);\n        if (userData) {\n          setUser(userData);\n        } else {\n          setError('用户不存在');\n        }\n      } catch (err) {\n        setError('加载用户信息失败');\n      } finally {\n        setLoading(false);\n      }\n    }\n  }, [userId]);\n\n  const handleDeleteUser = () => {\n    if (user && window.confirm(`确定要删除用户 \"${user.name}\" 吗？此操作不可撤销。`)) {\n      try {\n        deleteUser(user.id);\n        router.push('/users?success=deleted');\n      } catch (error) {\n        alert('删除用户失败');\n      }\n    }\n  };\n\n  // 获取状态显示样式\n  const getStatusBadge = (status: string) => {\n    const styles = {\n      active: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',\n      inactive: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',\n      pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',\n    };\n    \n    const labels = {\n      active: '激活',\n      inactive: '未激活',\n      pending: '待审核',\n    };\n\n    return (\n      <span className={`px-3 py-1 text-sm font-medium rounded-full ${styles[status as keyof typeof styles]}`}>\n        {labels[status as keyof typeof labels]}\n      </span>\n    );\n  };\n\n  // 获取角色显示样式\n  const getRoleBadge = (role: string) => {\n    const styles = {\n      admin: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',\n      moderator: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',\n      user: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',\n    };\n    \n    const labels = {\n      admin: '超级管理员',\n      moderator: '管理员',\n      user: '普通用户',\n    };\n\n    return (\n      <span className={`px-3 py-1 text-sm font-medium rounded-full ${styles[role as keyof typeof styles]}`}>\n        {labels[role as keyof typeof labels]}\n      </span>\n    );\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n          <p className=\"mt-2 text-gray-600 dark:text-gray-300\">加载中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error || !user) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n        <div className=\"container mx-auto px-4 py-8\">\n          <div className=\"text-center\">\n            <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-4\">\n              {error || '用户不存在'}\n            </h1>\n            <Link\n              href=\"/users\"\n              className=\"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300\"\n            >\n              返回用户列表\n            </Link>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* 页面标题和导航 */}\n        <div className=\"mb-8\">\n          <nav className=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-4\">\n            <Link href=\"/\" className=\"hover:text-gray-700 dark:hover:text-gray-200\">\n              首页\n            </Link>\n            <span>/</span>\n            <Link href=\"/users\" className=\"hover:text-gray-700 dark:hover:text-gray-200\">\n              用户管理\n            </Link>\n            <span>/</span>\n            <span className=\"text-gray-900 dark:text-white\">用户详情</span>\n          </nav>\n          \n          <div className=\"flex items-center justify-between\">\n            <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n              用户详情\n            </h1>\n            <div className=\"flex items-center space-x-3\">\n              <Link\n                href={`/users/${user.id}/edit`}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center space-x-2\"\n              >\n                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                </svg>\n                <span>编辑</span>\n              </Link>\n              <button\n                onClick={handleDeleteUser}\n                className=\"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center space-x-2\"\n              >\n                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                </svg>\n                <span>删除</span>\n              </button>\n              <Link\n                href=\"/users\"\n                className=\"px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-colors\"\n              >\n                返回列表\n              </Link>\n            </div>\n          </div>\n        </div>\n\n        {/* 用户信息卡片 */}\n        <div className=\"max-w-4xl mx-auto\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden\">\n            {/* 用户头部信息 */}\n            <div className=\"bg-gradient-to-r from-blue-500 to-purple-600 px-6 py-8\">\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"w-20 h-20 bg-white rounded-full flex items-center justify-center\">\n                  <span className=\"text-2xl font-bold text-gray-600\">\n                    {user.name.charAt(0)}\n                  </span>\n                </div>\n                <div className=\"text-white\">\n                  <h2 className=\"text-2xl font-bold\">{user.name}</h2>\n                  <p className=\"text-blue-100\">{user.email}</p>\n                  {user.phone && (\n                    <p className=\"text-blue-100\">{user.phone}</p>\n                  )}\n                </div>\n              </div>\n            </div>\n\n            {/* 详细信息 */}\n            <div className=\"p-6\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                {/* 基本信息 */}\n                <div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                    基本信息\n                  </h3>\n                  <div className=\"space-y-3\">\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-gray-600 dark:text-gray-400\">用户ID:</span>\n                      <span className=\"text-gray-900 dark:text-white font-mono text-sm\">{user.id}</span>\n                    </div>\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-gray-600 dark:text-gray-400\">角色:</span>\n                      {getRoleBadge(user.role)}\n                    </div>\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-gray-600 dark:text-gray-400\">状态:</span>\n                      {getStatusBadge(user.status)}\n                    </div>\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-gray-600 dark:text-gray-400\">邮箱:</span>\n                      <span className=\"text-gray-900 dark:text-white\">{user.email}</span>\n                    </div>\n                    {user.phone && (\n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-gray-600 dark:text-gray-400\">手机号:</span>\n                        <span className=\"text-gray-900 dark:text-white\">{user.phone}</span>\n                      </div>\n                    )}\n                  </div>\n                </div>\n\n                {/* 工作信息 */}\n                <div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                    工作信息\n                  </h3>\n                  <div className=\"space-y-3\">\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-gray-600 dark:text-gray-400\">部门:</span>\n                      <span className=\"text-gray-900 dark:text-white\">{user.department || '未设置'}</span>\n                    </div>\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-gray-600 dark:text-gray-400\">职位:</span>\n                      <span className=\"text-gray-900 dark:text-white\">{user.position || '未设置'}</span>\n                    </div>\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-gray-600 dark:text-gray-400\">创建时间:</span>\n                      <span className=\"text-gray-900 dark:text-white\">\n                        {new Date(user.createdAt).toLocaleString('zh-CN')}\n                      </span>\n                    </div>\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-gray-600 dark:text-gray-400\">更新时间:</span>\n                      <span className=\"text-gray-900 dark:text-white\">\n                        {new Date(user.updatedAt).toLocaleString('zh-CN')}\n                      </span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,SAAS,OAAO,EAAE;IAExB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,IAAI;gBACF,MAAM,WAAW,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE;gBAC7B,IAAI,UAAU;oBACZ,QAAQ;gBACV,OAAO;oBACL,SAAS;gBACX;YACF,EAAE,OAAO,KAAK;gBACZ,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,mBAAmB;QACvB,IAAI,QAAQ,OAAO,OAAO,CAAC,CAAC,SAAS,EAAE,KAAK,IAAI,CAAC,YAAY,CAAC,GAAG;YAC/D,IAAI;gBACF,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,EAAE;gBAClB,OAAO,IAAI,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,MAAM;YACR;QACF;IACF;IAEA,WAAW;IACX,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAS;YACb,QAAQ;YACR,UAAU;YACV,SAAS;QACX;QAEA,MAAM,SAAS;YACb,QAAQ;YACR,UAAU;YACV,SAAS;QACX;QAEA,qBACE,8OAAC;YAAK,WAAW,CAAC,2CAA2C,EAAE,MAAM,CAAC,OAA8B,EAAE;sBACnG,MAAM,CAAC,OAA8B;;;;;;IAG5C;IAEA,WAAW;IACX,MAAM,eAAe,CAAC;QACpB,MAAM,SAAS;YACb,OAAO;YACP,WAAW;YACX,MAAM;QACR;QAEA,MAAM,SAAS;YACb,OAAO;YACP,WAAW;YACX,MAAM;QACR;QAEA,qBACE,8OAAC;YAAK,WAAW,CAAC,2CAA2C,EAAE,MAAM,CAAC,KAA4B,EAAE;sBACjG,MAAM,CAAC,KAA4B;;;;;;IAG1C;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAwC;;;;;;;;;;;;;;;;;IAI7D;IAEA,IAAI,SAAS,CAAC,MAAM;QAClB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,SAAS;;;;;;sCAEZ,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAA+C;;;;;;8CAGxE,8OAAC;8CAAK;;;;;;8CACN,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAA+C;;;;;;8CAG7E,8OAAC;8CAAK;;;;;;8CACN,8OAAC;oCAAK,WAAU;8CAAgC;;;;;;;;;;;;sCAGlD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmD;;;;;;8CAGjE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;4CAC9B,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;8DAEvE,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CACC,SAAS;4CACT,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;8DAEvE,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;8BAQP,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DACb,KAAK,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;sDAGtB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAsB,KAAK,IAAI;;;;;;8DAC7C,8OAAC;oDAAE,WAAU;8DAAiB,KAAK,KAAK;;;;;;gDACvC,KAAK,KAAK,kBACT,8OAAC;oDAAE,WAAU;8DAAiB,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;0CAOhD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA2D;;;;;;8DAGzE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAmC;;;;;;8EACnD,8OAAC;oEAAK,WAAU;8EAAmD,KAAK,EAAE;;;;;;;;;;;;sEAE5E,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAmC;;;;;;gEAClD,aAAa,KAAK,IAAI;;;;;;;sEAEzB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAmC;;;;;;gEAClD,eAAe,KAAK,MAAM;;;;;;;sEAE7B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAmC;;;;;;8EACnD,8OAAC;oEAAK,WAAU;8EAAiC,KAAK,KAAK;;;;;;;;;;;;wDAE5D,KAAK,KAAK,kBACT,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAmC;;;;;;8EACnD,8OAAC;oEAAK,WAAU;8EAAiC,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;sDAOnE,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA2D;;;;;;8DAGzE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAmC;;;;;;8EACnD,8OAAC;oEAAK,WAAU;8EAAiC,KAAK,UAAU,IAAI;;;;;;;;;;;;sEAEtE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAmC;;;;;;8EACnD,8OAAC;oEAAK,WAAU;8EAAiC,KAAK,QAAQ,IAAI;;;;;;;;;;;;sEAEpE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAmC;;;;;;8EACnD,8OAAC;oEAAK,WAAU;8EACb,IAAI,KAAK,KAAK,SAAS,EAAE,cAAc,CAAC;;;;;;;;;;;;sEAG7C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAmC;;;;;;8EACnD,8OAAC;oEAAK,WAAU;8EACb,IAAI,KAAK,KAAK,SAAS,EAAE,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYjE", "debugId": null}}]}
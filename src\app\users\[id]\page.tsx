'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { User } from '@/types/user';
import { getUserById, deleteUser } from '@/lib/users';

export default function UserDetailPage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const userId = params.id as string;

  // 检查是否有成功消息
  useEffect(() => {
    const success = searchParams.get('success');
    if (success === 'updated') {
      setSuccessMessage('用户信息更新成功！');
      setTimeout(() => setSuccessMessage(null), 3000);
    }
  }, [searchParams]);

  useEffect(() => {
    if (userId) {
      try {
        const userData = getUserById(userId);
        if (userData) {
          setUser(userData);
        } else {
          setError('用户不存在');
        }
      } catch (err) {
        setError('加载用户信息失败');
      } finally {
        setLoading(false);
      }
    }
  }, [userId]);

  const handleDeleteUser = () => {
    if (user && window.confirm(`确定要删除用户 "${user.name}" 吗？此操作不可撤销。`)) {
      try {
        deleteUser(user.id);
        router.push('/users?success=deleted');
      } catch (error) {
        alert('删除用户失败');
      }
    }
  };

  // 获取状态显示样式
  const getStatusBadge = (status: string) => {
    const styles = {
      active: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      inactive: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
      pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    };
    
    const labels = {
      active: '激活',
      inactive: '未激活',
      pending: '待审核',
    };

    return (
      <span className={`px-3 py-1 text-sm font-medium rounded-full ${styles[status as keyof typeof styles]}`}>
        {labels[status as keyof typeof labels]}
      </span>
    );
  };

  // 获取角色显示样式
  const getRoleBadge = (role: string) => {
    const styles = {
      admin: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
      moderator: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      user: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
    };
    
    const labels = {
      admin: '超级管理员',
      moderator: '管理员',
      user: '普通用户',
    };

    return (
      <span className={`px-3 py-1 text-sm font-medium rounded-full ${styles[role as keyof typeof styles]}`}>
        {labels[role as keyof typeof labels]}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p className="mt-2 text-gray-600 dark:text-gray-300">加载中...</p>
        </div>
      </div>
    );
  }

  if (error || !user) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              {error || '用户不存在'}
            </h1>
            <Link
              href="/users"
              className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
            >
              返回用户列表
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* 页面标题和导航 */}
        <div className="mb-8">
          <nav className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-4">
            <Link href="/" className="hover:text-gray-700 dark:hover:text-gray-200">
              首页
            </Link>
            <span>/</span>
            <Link href="/users" className="hover:text-gray-700 dark:hover:text-gray-200">
              用户管理
            </Link>
            <span>/</span>
            <span className="text-gray-900 dark:text-white">用户详情</span>
          </nav>
          
          <div className="flex items-center justify-between">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              用户详情
            </h1>
            <div className="flex items-center space-x-3">
              <Link
                href={`/users/${user.id}/edit`}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center space-x-2"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                <span>编辑</span>
              </Link>
              <button
                onClick={handleDeleteUser}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center space-x-2"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                <span>删除</span>
              </button>
              <Link
                href="/users"
                className="px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-colors"
              >
                返回列表
              </Link>
            </div>
          </div>
        </div>

        {/* 用户信息卡片 */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
            {/* 用户头部信息 */}
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 px-6 py-8">
              <div className="flex items-center space-x-4">
                <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center">
                  <span className="text-2xl font-bold text-gray-600">
                    {user.name.charAt(0)}
                  </span>
                </div>
                <div className="text-white">
                  <h2 className="text-2xl font-bold">{user.name}</h2>
                  <p className="text-blue-100">{user.email}</p>
                  {user.phone && (
                    <p className="text-blue-100">{user.phone}</p>
                  )}
                </div>
              </div>
            </div>

            {/* 详细信息 */}
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* 基本信息 */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    基本信息
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 dark:text-gray-400">用户ID:</span>
                      <span className="text-gray-900 dark:text-white font-mono text-sm">{user.id}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 dark:text-gray-400">角色:</span>
                      {getRoleBadge(user.role)}
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 dark:text-gray-400">状态:</span>
                      {getStatusBadge(user.status)}
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 dark:text-gray-400">邮箱:</span>
                      <span className="text-gray-900 dark:text-white">{user.email}</span>
                    </div>
                    {user.phone && (
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600 dark:text-gray-400">手机号:</span>
                        <span className="text-gray-900 dark:text-white">{user.phone}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* 工作信息 */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    工作信息
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 dark:text-gray-400">部门:</span>
                      <span className="text-gray-900 dark:text-white">{user.department || '未设置'}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 dark:text-gray-400">职位:</span>
                      <span className="text-gray-900 dark:text-white">{user.position || '未设置'}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 dark:text-gray-400">创建时间:</span>
                      <span className="text-gray-900 dark:text-white">
                        {new Date(user.createdAt).toLocaleString('zh-CN')}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 dark:text-gray-400">更新时间:</span>
                      <span className="text-gray-900 dark:text-white">
                        {new Date(user.updatedAt).toLocaleString('zh-CN')}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

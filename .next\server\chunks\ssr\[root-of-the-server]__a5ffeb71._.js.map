{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/src/lib/users.ts"], "sourcesContent": ["import { User, CreateUserData, UpdateUserData, UserFilters, PaginationParams, UserListResponse } from '@/types/user';\n\n// 模拟用户数据存储\nlet users: User[] = [\n  {\n    id: '1',\n    name: '张三',\n    email: '<PERSON><PERSON><PERSON>@example.com',\n    phone: '13800138001',\n    role: 'admin',\n    status: 'active',\n    department: '技术部',\n    position: '高级工程师',\n    createdAt: '2024-01-15T08:00:00Z',\n    updatedAt: '2024-01-15T08:00:00Z',\n  },\n  {\n    id: '2',\n    name: '李四',\n    email: '<EMAIL>',\n    phone: '13800138002',\n    role: 'user',\n    status: 'active',\n    department: '产品部',\n    position: '产品经理',\n    createdAt: '2024-01-16T09:00:00Z',\n    updatedAt: '2024-01-16T09:00:00Z',\n  },\n  {\n    id: '3',\n    name: '王五',\n    email: '<EMAIL>',\n    phone: '13800138003',\n    role: 'moderator',\n    status: 'pending',\n    department: '运营部',\n    position: '运营专员',\n    createdAt: '2024-01-17T10:00:00Z',\n    updatedAt: '2024-01-17T10:00:00Z',\n  },\n];\n\n// 生成唯一ID\nfunction generateId(): string {\n  return Date.now().toString() + Math.random().toString(36).substr(2, 9);\n}\n\n// 获取用户列表\nexport function getUsers(filters?: UserFilters, pagination?: PaginationParams): UserListResponse {\n  let filteredUsers = [...users];\n\n  // 应用筛选\n  if (filters) {\n    if (filters.search) {\n      const searchLower = filters.search.toLowerCase();\n      filteredUsers = filteredUsers.filter(user =>\n        user.name.toLowerCase().includes(searchLower) ||\n        user.email.toLowerCase().includes(searchLower) ||\n        user.phone?.toLowerCase().includes(searchLower)\n      );\n    }\n\n    if (filters.role) {\n      filteredUsers = filteredUsers.filter(user => user.role === filters.role);\n    }\n\n    if (filters.status) {\n      filteredUsers = filteredUsers.filter(user => user.status === filters.status);\n    }\n\n    if (filters.department) {\n      filteredUsers = filteredUsers.filter(user => user.department === filters.department);\n    }\n  }\n\n  const total = filteredUsers.length;\n\n  // 应用分页\n  if (pagination) {\n    const startIndex = (pagination.page - 1) * pagination.limit;\n    const endIndex = startIndex + pagination.limit;\n    filteredUsers = filteredUsers.slice(startIndex, endIndex);\n  }\n\n  return {\n    users: filteredUsers,\n    total,\n    page: pagination?.page || 1,\n    limit: pagination?.limit || total,\n    totalPages: pagination ? Math.ceil(total / pagination.limit) : 1,\n  };\n}\n\n// 根据ID获取用户\nexport function getUserById(id: string): User | null {\n  return users.find(user => user.id === id) || null;\n}\n\n// 创建用户\nexport function createUser(userData: CreateUserData): User {\n  // 检查邮箱是否已存在\n  const existingUser = users.find(user => user.email === userData.email);\n  if (existingUser) {\n    throw new Error('邮箱已存在');\n  }\n\n  const now = new Date().toISOString();\n  const newUser: User = {\n    id: generateId(),\n    ...userData,\n    createdAt: now,\n    updatedAt: now,\n  };\n\n  users.push(newUser);\n  return newUser;\n}\n\n// 更新用户\nexport function updateUser(userData: UpdateUserData): User {\n  const userIndex = users.findIndex(user => user.id === userData.id);\n  if (userIndex === -1) {\n    throw new Error('用户不存在');\n  }\n\n  // 检查邮箱是否被其他用户使用\n  if (userData.email) {\n    const existingUser = users.find(user => user.email === userData.email && user.id !== userData.id);\n    if (existingUser) {\n      throw new Error('邮箱已被其他用户使用');\n    }\n  }\n\n  const updatedUser: User = {\n    ...users[userIndex],\n    ...userData,\n    updatedAt: new Date().toISOString(),\n  };\n\n  users[userIndex] = updatedUser;\n  return updatedUser;\n}\n\n// 删除用户\nexport function deleteUser(id: string): boolean {\n  const userIndex = users.findIndex(user => user.id === id);\n  if (userIndex === -1) {\n    return false;\n  }\n\n  users.splice(userIndex, 1);\n  return true;\n}\n\n// 获取统计信息\nexport function getUserStats() {\n  const total = users.length;\n  const active = users.filter(user => user.status === 'active').length;\n  const inactive = users.filter(user => user.status === 'inactive').length;\n  const pending = users.filter(user => user.status === 'pending').length;\n\n  const roleStats = {\n    admin: users.filter(user => user.role === 'admin').length,\n    user: users.filter(user => user.role === 'user').length,\n    moderator: users.filter(user => user.role === 'moderator').length,\n  };\n\n  return {\n    total,\n    active,\n    inactive,\n    pending,\n    roleStats,\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA,WAAW;AACX,IAAI,QAAgB;IAClB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,UAAU;QACV,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,UAAU;QACV,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,UAAU;QACV,WAAW;QACX,WAAW;IACb;CACD;AAED,SAAS;AACT,SAAS;IACP,OAAO,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AACtE;AAGO,SAAS,SAAS,OAAqB,EAAE,UAA6B;IAC3E,IAAI,gBAAgB;WAAI;KAAM;IAE9B,OAAO;IACP,IAAI,SAAS;QACX,IAAI,QAAQ,MAAM,EAAE;YAClB,MAAM,cAAc,QAAQ,MAAM,CAAC,WAAW;YAC9C,gBAAgB,cAAc,MAAM,CAAC,CAAA,OACnC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACjC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAClC,KAAK,KAAK,EAAE,cAAc,SAAS;QAEvC;QAEA,IAAI,QAAQ,IAAI,EAAE;YAChB,gBAAgB,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,QAAQ,IAAI;QACzE;QAEA,IAAI,QAAQ,MAAM,EAAE;YAClB,gBAAgB,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,QAAQ,MAAM;QAC7E;QAEA,IAAI,QAAQ,UAAU,EAAE;YACtB,gBAAgB,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,UAAU,KAAK,QAAQ,UAAU;QACrF;IACF;IAEA,MAAM,QAAQ,cAAc,MAAM;IAElC,OAAO;IACP,IAAI,YAAY;QACd,MAAM,aAAa,CAAC,WAAW,IAAI,GAAG,CAAC,IAAI,WAAW,KAAK;QAC3D,MAAM,WAAW,aAAa,WAAW,KAAK;QAC9C,gBAAgB,cAAc,KAAK,CAAC,YAAY;IAClD;IAEA,OAAO;QACL,OAAO;QACP;QACA,MAAM,YAAY,QAAQ;QAC1B,OAAO,YAAY,SAAS;QAC5B,YAAY,aAAa,KAAK,IAAI,CAAC,QAAQ,WAAW,KAAK,IAAI;IACjE;AACF;AAGO,SAAS,YAAY,EAAU;IACpC,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO;AAC/C;AAGO,SAAS,WAAW,QAAwB;IACjD,YAAY;IACZ,MAAM,eAAe,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK,SAAS,KAAK;IACrE,IAAI,cAAc;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,MAAM,IAAI,OAAO,WAAW;IAClC,MAAM,UAAgB;QACpB,IAAI;QACJ,GAAG,QAAQ;QACX,WAAW;QACX,WAAW;IACb;IAEA,MAAM,IAAI,CAAC;IACX,OAAO;AACT;AAGO,SAAS,WAAW,QAAwB;IACjD,MAAM,YAAY,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,SAAS,EAAE;IACjE,IAAI,cAAc,CAAC,GAAG;QACpB,MAAM,IAAI,MAAM;IAClB;IAEA,gBAAgB;IAChB,IAAI,SAAS,KAAK,EAAE;QAClB,MAAM,eAAe,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK,SAAS,KAAK,IAAI,KAAK,EAAE,KAAK,SAAS,EAAE;QAChG,IAAI,cAAc;YAChB,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAM,cAAoB;QACxB,GAAG,KAAK,CAAC,UAAU;QACnB,GAAG,QAAQ;QACX,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,KAAK,CAAC,UAAU,GAAG;IACnB,OAAO;AACT;AAGO,SAAS,WAAW,EAAU;IACnC,MAAM,YAAY,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IACtD,IAAI,cAAc,CAAC,GAAG;QACpB,OAAO;IACT;IAEA,MAAM,MAAM,CAAC,WAAW;IACxB,OAAO;AACT;AAGO,SAAS;IACd,MAAM,QAAQ,MAAM,MAAM;IAC1B,MAAM,SAAS,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,UAAU,MAAM;IACpE,MAAM,WAAW,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,YAAY,MAAM;IACxE,MAAM,UAAU,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,WAAW,MAAM;IAEtE,MAAM,YAAY;QAChB,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,SAAS,MAAM;QACzD,MAAM,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,QAAQ,MAAM;QACvD,WAAW,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,aAAa,MAAM;IACnE;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/src/lib/auth.ts"], "sourcesContent": ["import { LoginCredentials, AuthUser, AuthSession, LoginResponse } from '@/types/auth';\nimport { getUserById } from './users';\n\n// 模拟用户凭据数据库\nconst userCredentials = [\n  {\n    email: '<EMAIL>',\n    password: 'admin123',\n    userId: '1'\n  },\n  {\n    email: '<PERSON><PERSON><PERSON>@example.com',\n    password: 'password123',\n    userId: '1'\n  },\n  {\n    email: '<EMAIL>',\n    password: 'password123',\n    userId: '2'\n  },\n  {\n    email: '<EMAIL>',\n    password: 'password123',\n    userId: '3'\n  }\n];\n\n// 生成JWT token（模拟）\nfunction generateToken(userId: string): string {\n  const payload = {\n    userId,\n    exp: Date.now() + 24 * 60 * 60 * 1000, // 24小时过期\n  };\n  return btoa(JSON.stringify(payload));\n}\n\n// 验证token\nfunction verifyToken(token: string): { userId: string; exp: number } | null {\n  try {\n    const payload = JSON.parse(atob(token));\n    if (payload.exp < Date.now()) {\n      return null; // token已过期\n    }\n    return payload;\n  } catch {\n    return null; // token无效\n  }\n}\n\n// 登录函数\nexport async function login(credentials: LoginCredentials): Promise<LoginResponse> {\n  // 模拟网络延迟\n  await new Promise(resolve => setTimeout(resolve, 1000));\n\n  // 查找用户凭据\n  const userCred = userCredentials.find(\n    cred => cred.email === credentials.email && cred.password === credentials.password\n  );\n\n  if (!userCred) {\n    return {\n      success: false,\n      message: '邮箱或密码错误'\n    };\n  }\n\n  // 获取用户信息\n  const user = getUserById(userCred.userId);\n  if (!user) {\n    return {\n      success: false,\n      message: '用户不存在'\n    };\n  }\n\n  // 检查用户状态\n  if (user.status !== 'active') {\n    return {\n      success: false,\n      message: '账户未激活或已被禁用'\n    };\n  }\n\n  // 生成token\n  const token = generateToken(user.id);\n\n  // 转换为AuthUser格式\n  const authUser: AuthUser = {\n    id: user.id,\n    name: user.name,\n    email: user.email,\n    role: user.role,\n    status: user.status,\n    avatar: user.avatar\n  };\n\n  return {\n    success: true,\n    user: authUser,\n    token\n  };\n}\n\n// 验证当前会话\nexport function validateSession(token: string): AuthUser | null {\n  const payload = verifyToken(token);\n  if (!payload) {\n    return null;\n  }\n\n  const user = getUserById(payload.userId);\n  if (!user || user.status !== 'active') {\n    return null;\n  }\n\n  return {\n    id: user.id,\n    name: user.name,\n    email: user.email,\n    role: user.role,\n    status: user.status,\n    avatar: user.avatar\n  };\n}\n\n// 本地存储管理\nexport const storage = {\n  setToken: (token: string) => {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('auth_token', token);\n    }\n  },\n\n  getToken: (): string | null => {\n    if (typeof window !== 'undefined') {\n      return localStorage.getItem('auth_token');\n    }\n    return null;\n  },\n\n  removeToken: () => {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('auth_token');\n    }\n  },\n\n  setUser: (user: AuthUser) => {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('auth_user', JSON.stringify(user));\n    }\n  },\n\n  getUser: (): AuthUser | null => {\n    if (typeof window !== 'undefined') {\n      const userStr = localStorage.getItem('auth_user');\n      if (userStr) {\n        try {\n          return JSON.parse(userStr);\n        } catch {\n          return null;\n        }\n      }\n    }\n    return null;\n  },\n\n  removeUser: () => {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('auth_user');\n    }\n  },\n\n  clear: () => {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('auth_token');\n      localStorage.removeItem('auth_user');\n    }\n  }\n};\n\n// 登出函数\nexport function logout() {\n  storage.clear();\n}\n\n// 检查用户权限\nexport function hasPermission(user: AuthUser | null, requiredRole: 'admin' | 'moderator' | 'user'): boolean {\n  if (!user) return false;\n\n  const roleHierarchy = {\n    admin: 3,\n    moderator: 2,\n    user: 1\n  };\n\n  return roleHierarchy[user.role] >= roleHierarchy[requiredRole];\n}\n\n// 获取当前认证状态\nexport function getCurrentAuth(): { user: AuthUser | null; token: string | null } {\n  const token = storage.getToken();\n  const user = storage.getUser();\n\n  if (!token || !user) {\n    return { user: null, token: null };\n  }\n\n  // 验证token是否有效\n  const validatedUser = validateSession(token);\n  if (!validatedUser) {\n    storage.clear();\n    return { user: null, token: null };\n  }\n\n  return { user: validatedUser, token };\n}\n"], "names": [], "mappings": ";;;;;;;;AACA;;AAEA,YAAY;AACZ,MAAM,kBAAkB;IACtB;QACE,OAAO;QACP,UAAU;QACV,QAAQ;IACV;IACA;QACE,OAAO;QACP,UAAU;QACV,QAAQ;IACV;IACA;QACE,OAAO;QACP,UAAU;QACV,QAAQ;IACV;IACA;QACE,OAAO;QACP,UAAU;QACV,QAAQ;IACV;CACD;AAED,kBAAkB;AAClB,SAAS,cAAc,MAAc;IACnC,MAAM,UAAU;QACd;QACA,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK;IACnC;IACA,OAAO,KAAK,KAAK,SAAS,CAAC;AAC7B;AAEA,UAAU;AACV,SAAS,YAAY,KAAa;IAChC,IAAI;QACF,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK;QAChC,IAAI,QAAQ,GAAG,GAAG,KAAK,GAAG,IAAI;YAC5B,OAAO,MAAM,WAAW;QAC1B;QACA,OAAO;IACT,EAAE,OAAM;QACN,OAAO,MAAM,UAAU;IACzB;AACF;AAGO,eAAe,MAAM,WAA6B;IACvD,SAAS;IACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;IAEjD,SAAS;IACT,MAAM,WAAW,gBAAgB,IAAI,CACnC,CAAA,OAAQ,KAAK,KAAK,KAAK,YAAY,KAAK,IAAI,KAAK,QAAQ,KAAK,YAAY,QAAQ;IAGpF,IAAI,CAAC,UAAU;QACb,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF;IAEA,SAAS;IACT,MAAM,OAAO,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,SAAS,MAAM;IACxC,IAAI,CAAC,MAAM;QACT,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF;IAEA,SAAS;IACT,IAAI,KAAK,MAAM,KAAK,UAAU;QAC5B,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF;IAEA,UAAU;IACV,MAAM,QAAQ,cAAc,KAAK,EAAE;IAEnC,gBAAgB;IAChB,MAAM,WAAqB;QACzB,IAAI,KAAK,EAAE;QACX,MAAM,KAAK,IAAI;QACf,OAAO,KAAK,KAAK;QACjB,MAAM,KAAK,IAAI;QACf,QAAQ,KAAK,MAAM;QACnB,QAAQ,KAAK,MAAM;IACrB;IAEA,OAAO;QACL,SAAS;QACT,MAAM;QACN;IACF;AACF;AAGO,SAAS,gBAAgB,KAAa;IAC3C,MAAM,UAAU,YAAY;IAC5B,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,MAAM,OAAO,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,MAAM;IACvC,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,UAAU;QACrC,OAAO;IACT;IAEA,OAAO;QACL,IAAI,KAAK,EAAE;QACX,MAAM,KAAK,IAAI;QACf,OAAO,KAAK,KAAK;QACjB,MAAM,KAAK,IAAI;QACf,QAAQ,KAAK,MAAM;QACnB,QAAQ,KAAK,MAAM;IACrB;AACF;AAGO,MAAM,UAAU;IACrB,UAAU,CAAC;QACT,uCAAmC;;QAEnC;IACF;IAEA,UAAU;QACR,uCAAmC;;QAEnC;QACA,OAAO;IACT;IAEA,aAAa;QACX,uCAAmC;;QAEnC;IACF;IAEA,SAAS,CAAC;QACR,uCAAmC;;QAEnC;IACF;IAEA,SAAS;QACP,uCAAmC;;QASnC;QACA,OAAO;IACT;IAEA,YAAY;QACV,uCAAmC;;QAEnC;IACF;IAEA,OAAO;QACL,uCAAmC;;QAGnC;IACF;AACF;AAGO,SAAS;IACd,QAAQ,KAAK;AACf;AAGO,SAAS,cAAc,IAAqB,EAAE,YAA4C;IAC/F,IAAI,CAAC,MAAM,OAAO;IAElB,MAAM,gBAAgB;QACpB,OAAO;QACP,WAAW;QACX,MAAM;IACR;IAEA,OAAO,aAAa,CAAC,KAAK,IAAI,CAAC,IAAI,aAAa,CAAC,aAAa;AAChE;AAGO,SAAS;IACd,MAAM,QAAQ,QAAQ,QAAQ;IAC9B,MAAM,OAAO,QAAQ,OAAO;IAE5B,IAAI,CAAC,SAAS,CAAC,MAAM;QACnB,OAAO;YAAE,MAAM;YAAM,OAAO;QAAK;IACnC;IAEA,cAAc;IACd,MAAM,gBAAgB,gBAAgB;IACtC,IAAI,CAAC,eAAe;QAClB,QAAQ,KAAK;QACb,OAAO;YAAE,MAAM;YAAM,OAAO;QAAK;IACnC;IAEA,OAAO;QAAE,MAAM;QAAe;IAAM;AACtC", "debugId": null}}, {"offset": {"line": 390, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { AuthContextType, AuthUser, LoginCredentials } from '@/types/auth';\nimport { login as authLogin, storage, validateSession, logout as authLogout } from '@/lib/auth';\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<AuthUser | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // 检查认证状态\n  const checkAuth = () => {\n    const token = storage.getToken();\n    const storedUser = storage.getUser();\n\n    if (token && storedUser) {\n      // 验证token是否有效\n      const validatedUser = validateSession(token);\n      if (validatedUser) {\n        setUser(validatedUser);\n      } else {\n        // token无效，清除存储\n        storage.clear();\n        setUser(null);\n      }\n    } else {\n      setUser(null);\n    }\n    setIsLoading(false);\n  };\n\n  // 登录函数\n  const login = async (credentials: LoginCredentials) => {\n    setIsLoading(true);\n    try {\n      const response = await authLogin(credentials);\n      \n      if (response.success && response.user && response.token) {\n        // 保存到本地存储\n        storage.setToken(response.token);\n        storage.setUser(response.user);\n        setUser(response.user);\n      } else {\n        throw new Error(response.message || '登录失败');\n      }\n    } catch (error) {\n      throw error;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // 登出函数\n  const logout = () => {\n    authLogout();\n    setUser(null);\n  };\n\n  // 初始化时检查认证状态\n  useEffect(() => {\n    checkAuth();\n  }, []);\n\n  const value: AuthContextType = {\n    user,\n    isLoading,\n    isAuthenticated: !!user,\n    login,\n    logout,\n    checkAuth,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAJA;;;;AAMA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,SAAS;IACT,MAAM,YAAY;QAChB,MAAM,QAAQ,kHAAA,CAAA,UAAO,CAAC,QAAQ;QAC9B,MAAM,aAAa,kHAAA,CAAA,UAAO,CAAC,OAAO;QAElC,IAAI,SAAS,YAAY;YACvB,cAAc;YACd,MAAM,gBAAgB,CAAA,GAAA,kHAAA,CAAA,kBAAe,AAAD,EAAE;YACtC,IAAI,eAAe;gBACjB,QAAQ;YACV,OAAO;gBACL,eAAe;gBACf,kHAAA,CAAA,UAAO,CAAC,KAAK;gBACb,QAAQ;YACV;QACF,OAAO;YACL,QAAQ;QACV;QACA,aAAa;IACf;IAEA,OAAO;IACP,MAAM,QAAQ,OAAO;QACnB,aAAa;QACb,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,kHAAA,CAAA,QAAS,AAAD,EAAE;YAEjC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,IAAI,SAAS,KAAK,EAAE;gBACvD,UAAU;gBACV,kHAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,SAAS,KAAK;gBAC/B,kHAAA,CAAA,UAAO,CAAC,OAAO,CAAC,SAAS,IAAI;gBAC7B,QAAQ,SAAS,IAAI;YACvB,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,OAAO,IAAI;YACtC;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,OAAO;IACP,MAAM,SAAS;QACb,CAAA,GAAA,kHAAA,CAAA,SAAU,AAAD;QACT,QAAQ;IACV;IAEA,aAAa;IACb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,QAAyB;QAC7B;QACA;QACA,iBAAiB,CAAC,CAAC;QACnB;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 482, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/src/components/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\n\ninterface MenuItem {\n  name: string;\n  href: string;\n  icon: React.ReactNode;\n  children?: MenuItem[];\n}\n\nconst menuItems: MenuItem[] = [\n  {\n    name: '首页',\n    href: '/',\n    icon: (\n      <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" />\n      </svg>\n    ),\n  },\n  {\n    name: '用户管理',\n    href: '/users',\n    icon: (\n      <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\" />\n      </svg>\n    ),\n    children: [\n      {\n        name: '用户列表',\n        href: '/users',\n        icon: (\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n          </svg>\n        ),\n      },\n      {\n        name: '添加用户',\n        href: '/users/add',\n        icon: (\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n          </svg>\n        ),\n      },\n    ],\n  },\n  {\n    name: '统计分析',\n    href: '/analytics',\n    icon: (\n      <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n      </svg>\n    ),\n  },\n  {\n    name: '设置',\n    href: '/settings',\n    icon: (\n      <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n      </svg>\n    ),\n  },\n];\n\n// 子菜单组件，带有平滑动画\ninterface SubMenuProps {\n  children: MenuItem[];\n  isExpanded: boolean;\n  isCollapsed: boolean;\n  isActive: (href: string) => boolean;\n}\n\nfunction SubMenu({ children, isExpanded, isCollapsed, isActive }: SubMenuProps) {\n  const contentRef = useRef<HTMLDivElement>(null);\n  const [height, setHeight] = useState(0);\n\n  useEffect(() => {\n    if (contentRef.current) {\n      setHeight(isExpanded && !isCollapsed ? contentRef.current.scrollHeight : 0);\n    }\n  }, [isExpanded, isCollapsed]);\n\n  return (\n    <div\n      className=\"overflow-hidden transition-all duration-300 ease-in-out\"\n      style={{ height: `${height}px` }}\n    >\n      <div ref={contentRef} className=\"mt-2 ml-6 space-y-1\">\n        {children.map((child) => (\n          <Link\n            key={child.name}\n            href={child.href}\n            className={`flex items-center space-x-3 px-3 py-2 rounded-md text-sm transition-all duration-200 ${\n              isActive(child.href)\n                ? 'bg-blue-50 text-blue-600 dark:bg-blue-900/50 dark:text-blue-300 border-l-2 border-blue-600'\n                : 'text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-gray-200'\n            }`}\n          >\n            {child.icon}\n            <span>{child.name}</span>\n          </Link>\n        ))}\n      </div>\n    </div>\n  );\n}\n\nexport default function Sidebar() {\n  const pathname = usePathname();\n  const { user, logout, isAuthenticated } = useAuth();\n  const [expandedItems, setExpandedItems] = useState<string[]>(['用户管理']);\n  const [isCollapsed, setIsCollapsed] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  // 定义isActive函数（需要在useEffect之前定义）\n  const isActive = (href: string) => {\n    // 首页特殊处理\n    if (href === '/') {\n      return pathname === '/';\n    }\n\n    // 精确匹配\n    if (pathname === href) {\n      return true;\n    }\n\n    // 对于子路径，确保是完整的路径段匹配\n    // 例如：/users/add 应该匹配 /users，但 /users-admin 不应该匹配 /users\n    if (pathname.startsWith(href + '/')) {\n      return true;\n    }\n\n    return false;\n  };\n\n  // 自动展开包含当前页面的菜单项\n  useEffect(() => {\n    if (!isCollapsed) {\n      const itemsToExpand: string[] = [];\n      menuItems.forEach(item => {\n        if (item.children) {\n          const hasActiveChild = item.children.some(child => isActive(child.href));\n          if (hasActiveChild) {\n            itemsToExpand.push(item.name);\n          }\n        }\n      });\n\n      // 只有当需要展开的项目发生变化时才更新状态\n      setExpandedItems(prev => {\n        const newItems = [...new Set([...prev, ...itemsToExpand])];\n        return newItems.length !== prev.length || newItems.some(item => !prev.includes(item))\n          ? newItems\n          : prev;\n      });\n    }\n  }, [pathname, isCollapsed]);\n\n  // 当侧边栏收起时，关闭所有展开的子菜单\n  useEffect(() => {\n    if (isCollapsed) {\n      setExpandedItems([]);\n    }\n  }, [isCollapsed]);\n\n  const toggleExpanded = (itemName: string) => {\n    if (isCollapsed) return; // 收起状态下不允许展开子菜单\n\n    setExpandedItems(prev =>\n      prev.includes(itemName)\n        ? prev.filter(name => name !== itemName)\n        : [...prev, itemName]\n    );\n  };\n\n\n\n  const isParentActive = (item: MenuItem) => {\n    // 如果有子菜单，检查是否有子菜单项被激活\n    if (item.children) {\n      const hasActiveChild = item.children.some(child => isActive(child.href));\n      if (hasActiveChild) {\n        return true;\n      }\n    }\n\n    // 检查父菜单自身是否激活（对于没有子菜单的项目，或者直接访问父菜单路径的情况）\n    return isActive(item.href);\n  };\n\n  const toggleSidebar = () => {\n    setIsCollapsed(!isCollapsed);\n    setIsMobileMenuOpen(false);\n  };\n\n  const toggleMobileMenu = () => {\n    setIsMobileMenuOpen(!isMobileMenuOpen);\n  };\n\n  return (\n    <>\n      {/* 移动端菜单按钮 */}\n      <button\n        onClick={toggleMobileMenu}\n        className=\"fixed top-4 left-4 z-50 p-2 bg-white dark:bg-gray-800 rounded-md shadow-md lg:hidden\"\n      >\n        <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n        </svg>\n      </button>\n\n      {/* 侧边栏 */}\n      <div className={`fixed left-0 top-0 h-full bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transition-all duration-300 ease-in-out z-40 ${\n        isCollapsed ? 'w-16' : 'w-64'\n      } ${\n        isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'\n      }`}>\n        {/* 头部 */}\n        <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\">\n          <div className={`flex items-center transition-all duration-300 ${isCollapsed ? 'justify-center w-full' : 'space-x-2'}`}>\n            <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0\">\n              <span className=\"text-white font-bold text-sm\">U</span>\n            </div>\n            {!isCollapsed && (\n              <span className=\"text-lg font-semibold text-gray-900 dark:text-white whitespace-nowrap overflow-hidden\">\n                用户管理系统\n              </span>\n            )}\n          </div>\n          {!isCollapsed && (\n            <button\n              onClick={toggleSidebar}\n              className=\"p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex-shrink-0 hidden lg:block\"\n              title=\"收起侧边栏\"\n            >\n              <svg\n                className=\"w-5 h-5 text-gray-500 dark:text-gray-400 transition-transform duration-300\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 19l-7-7 7-7\" />\n              </svg>\n            </button>\n          )}\n        </div>\n\n        {/* 收起状态下的展开按钮 */}\n        {isCollapsed && (\n          <div className=\"p-4 border-b border-gray-200 dark:border-gray-700\">\n            <button\n              onClick={toggleSidebar}\n              className=\"w-full p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n              title=\"展开侧边栏\"\n            >\n              <svg\n                className=\"w-5 h-5 text-gray-500 dark:text-gray-400 transition-transform duration-300 mx-auto\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 5l7 7-7 7\" />\n              </svg>\n            </button>\n          </div>\n        )}\n\n        {/* 导航菜单 */}\n        <nav className=\"flex-1 overflow-y-auto p-4\">\n          <ul className=\"space-y-1\">\n            {menuItems.map((item) => (\n              <li key={item.name}>\n                {item.children ? (\n                  // 有子菜单的项目\n                  <div>\n                    <button\n                      onClick={() => toggleExpanded(item.name)}\n                      disabled={isCollapsed}\n                      className={`w-full flex items-center justify-between px-3 py-2.5 rounded-lg text-sm font-medium transition-all duration-200 group ${\n                        isParentActive(item)\n                          ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200'\n                          : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'\n                      } ${isCollapsed ? 'justify-center' : ''}`}\n                      title={isCollapsed ? item.name : undefined}\n                    >\n                      <div className={`flex items-center ${isCollapsed ? 'justify-center' : 'space-x-3'}`}>\n                        <span className=\"flex-shrink-0\">{item.icon}</span>\n                        {!isCollapsed && <span className=\"truncate\">{item.name}</span>}\n                      </div>\n                      {!isCollapsed && item.children && (\n                        <svg\n                          className={`w-4 h-4 transition-transform duration-300 flex-shrink-0 ${\n                            expandedItems.includes(item.name) ? 'rotate-90' : ''\n                          }`}\n                          fill=\"none\"\n                          stroke=\"currentColor\"\n                          viewBox=\"0 0 24 24\"\n                        >\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                        </svg>\n                      )}\n                    </button>\n\n                    {/* 子菜单 */}\n                    {item.children && (\n                      <SubMenu\n                        children={item.children}\n                        isExpanded={expandedItems.includes(item.name)}\n                        isCollapsed={isCollapsed}\n                        isActive={isActive}\n                      />\n                    )}\n                  </div>\n                ) : (\n                  // 普通菜单项\n                  <Link\n                    href={item.href}\n                    onClick={() => setIsMobileMenuOpen(false)}\n                    className={`flex items-center px-3 py-2.5 rounded-lg text-sm font-medium transition-all duration-200 group ${\n                      isActive(item.href)\n                        ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200'\n                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'\n                    } ${isCollapsed ? 'justify-center' : 'space-x-3'}`}\n                    title={isCollapsed ? item.name : undefined}\n                  >\n                    <span className=\"flex-shrink-0\">{item.icon}</span>\n                    {!isCollapsed && <span className=\"truncate\">{item.name}</span>}\n                  </Link>\n                )}\n              </li>\n            ))}\n          </ul>\n        </nav>\n\n        {/* 底部用户信息 */}\n        <div className=\"border-t border-gray-200 dark:border-gray-700 p-4\">\n          {isAuthenticated && user ? (\n            <div className={`flex items-center transition-all duration-300 ${isCollapsed ? 'justify-center' : 'space-x-3'}`}>\n              <div className=\"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0\">\n                <span className=\"text-sm font-medium text-white\">\n                  {user.name.charAt(0).toUpperCase()}\n                </span>\n              </div>\n              {!isCollapsed && (\n                <div className=\"flex-1 min-w-0 opacity-100 transition-opacity duration-300\">\n                  <p className=\"text-sm font-medium text-gray-900 dark:text-white truncate\">\n                    {user.name}\n                  </p>\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\n                    {user.email}\n                  </p>\n                </div>\n              )}\n              {!isCollapsed && (\n                <div className=\"flex items-center space-x-1\">\n                  <button\n                    onClick={logout}\n                    className=\"p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n                    title=\"登出\"\n                  >\n                    <svg className=\"w-4 h-4 text-gray-500 dark:text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\n                    </svg>\n                  </button>\n                </div>\n              )}\n            </div>\n          ) : (\n            <div className={`flex items-center transition-all duration-300 ${isCollapsed ? 'justify-center' : 'space-x-3'}`}>\n              {!isCollapsed ? (\n                <Link\n                  href=\"/login\"\n                  className=\"w-full px-3 py-2 text-sm font-medium text-center text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 border border-blue-600 dark:border-blue-400 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors\"\n                >\n                  登录\n                </Link>\n              ) : (\n                <Link\n                  href=\"/login\"\n                  className=\"p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n                  title=\"登录\"\n                >\n                  <svg className=\"w-5 h-5 text-gray-500 dark:text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1\" />\n                  </svg>\n                </Link>\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* 移动端遮罩 */}\n      {isMobileMenuOpen && (\n        <div\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden transition-opacity duration-300\"\n          onClick={() => setIsMobileMenuOpen(false)}\n        />\n      )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAcA,MAAM,YAAwB;IAC5B;QACE,MAAM;QACN,MAAM;QACN,oBACE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;IAG3E;IACA;QACE,MAAM;QACN,MAAM;QACN,oBACE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;QAGzE,UAAU;YACR;gBACE,MAAM;gBACN,MAAM;gBACN,oBACE,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAG3E;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,oBACE,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAG3E;SACD;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,oBACE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;IAG3E;IACA;QACE,MAAM;QACN,MAAM;QACN,oBACE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;;8BACjE,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;8BACrE,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;;IAG3E;CACD;AAUD,SAAS,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAgB;IAC5E,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,OAAO,EAAE;YACtB,UAAU,cAAc,CAAC,cAAc,WAAW,OAAO,CAAC,YAAY,GAAG;QAC3E;IACF,GAAG;QAAC;QAAY;KAAY;IAE5B,qBACE,8OAAC;QACC,WAAU;QACV,OAAO;YAAE,QAAQ,GAAG,OAAO,EAAE,CAAC;QAAC;kBAE/B,cAAA,8OAAC;YAAI,KAAK;YAAY,WAAU;sBAC7B,SAAS,GAAG,CAAC,CAAC,sBACb,8OAAC,4JAAA,CAAA,UAAI;oBAEH,MAAM,MAAM,IAAI;oBAChB,WAAW,CAAC,qFAAqF,EAC/F,SAAS,MAAM,IAAI,IACf,+FACA,4HACJ;;wBAED,MAAM,IAAI;sCACX,8OAAC;sCAAM,MAAM,IAAI;;;;;;;mBATZ,MAAM,IAAI;;;;;;;;;;;;;;;AAe3B;AAEe,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;KAAO;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,iCAAiC;IACjC,MAAM,WAAW,CAAC;QAChB,SAAS;QACT,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QAEA,OAAO;QACP,IAAI,aAAa,MAAM;YACrB,OAAO;QACT;QAEA,oBAAoB;QACpB,wDAAwD;QACxD,IAAI,SAAS,UAAU,CAAC,OAAO,MAAM;YACnC,OAAO;QACT;QAEA,OAAO;IACT;IAEA,iBAAiB;IACjB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa;YAChB,MAAM,gBAA0B,EAAE;YAClC,UAAU,OAAO,CAAC,CAAA;gBAChB,IAAI,KAAK,QAAQ,EAAE;oBACjB,MAAM,iBAAiB,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAA,QAAS,SAAS,MAAM,IAAI;oBACtE,IAAI,gBAAgB;wBAClB,cAAc,IAAI,CAAC,KAAK,IAAI;oBAC9B;gBACF;YACF;YAEA,uBAAuB;YACvB,iBAAiB,CAAA;gBACf,MAAM,WAAW;uBAAI,IAAI,IAAI;2BAAI;2BAAS;qBAAc;iBAAE;gBAC1D,OAAO,SAAS,MAAM,KAAK,KAAK,MAAM,IAAI,SAAS,IAAI,CAAC,CAAA,OAAQ,CAAC,KAAK,QAAQ,CAAC,SAC3E,WACA;YACN;QACF;IACF,GAAG;QAAC;QAAU;KAAY;IAE1B,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa;YACf,iBAAiB,EAAE;QACrB;IACF,GAAG;QAAC;KAAY;IAEhB,MAAM,iBAAiB,CAAC;QACtB,IAAI,aAAa,QAAQ,gBAAgB;QAEzC,iBAAiB,CAAA,OACf,KAAK,QAAQ,CAAC,YACV,KAAK,MAAM,CAAC,CAAA,OAAQ,SAAS,YAC7B;mBAAI;gBAAM;aAAS;IAE3B;IAIA,MAAM,iBAAiB,CAAC;QACtB,sBAAsB;QACtB,IAAI,KAAK,QAAQ,EAAE;YACjB,MAAM,iBAAiB,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAA,QAAS,SAAS,MAAM,IAAI;YACtE,IAAI,gBAAgB;gBAClB,OAAO;YACT;QACF;QAEA,yCAAyC;QACzC,OAAO,SAAS,KAAK,IAAI;IAC3B;IAEA,MAAM,gBAAgB;QACpB,eAAe,CAAC;QAChB,oBAAoB;IACtB;IAEA,MAAM,mBAAmB;QACvB,oBAAoB,CAAC;IACvB;IAEA,qBACE;;0BAEE,8OAAC;gBACC,SAAS;gBACT,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;;;;;0BAKzE,8OAAC;gBAAI,WAAW,CAAC,+IAA+I,EAC9J,cAAc,SAAS,OACxB,CAAC,EACA,mBAAmB,kBAAkB,sCACrC;;kCAEA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAW,CAAC,8CAA8C,EAAE,cAAc,0BAA0B,aAAa;;kDACpH,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;oCAEhD,CAAC,6BACA,8OAAC;wCAAK,WAAU;kDAAwF;;;;;;;;;;;;4BAK3G,CAAC,6BACA,8OAAC;gCACC,SAAS;gCACT,WAAU;gCACV,OAAM;0CAEN,cAAA,8OAAC;oCACC,WAAU;oCACV,MAAK;oCACL,QAAO;oCACP,SAAQ;8CAER,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;oBAO5E,6BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS;4BACT,WAAU;4BACV,OAAM;sCAEN,cAAA,8OAAC;gCACC,WAAU;gCACV,MAAK;gCACL,QAAO;gCACP,SAAQ;0CAER,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;kCAO7E,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCACX,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC;8CACE,KAAK,QAAQ,GACZ,UAAU;kDACV,8OAAC;;0DACC,8OAAC;gDACC,SAAS,IAAM,eAAe,KAAK,IAAI;gDACvC,UAAU;gDACV,WAAW,CAAC,sHAAsH,EAChI,eAAe,QACX,kEACA,yHACL,CAAC,EAAE,cAAc,mBAAmB,IAAI;gDACzC,OAAO,cAAc,KAAK,IAAI,GAAG;;kEAEjC,8OAAC;wDAAI,WAAW,CAAC,kBAAkB,EAAE,cAAc,mBAAmB,aAAa;;0EACjF,8OAAC;gEAAK,WAAU;0EAAiB,KAAK,IAAI;;;;;;4DACzC,CAAC,6BAAe,8OAAC;gEAAK,WAAU;0EAAY,KAAK,IAAI;;;;;;;;;;;;oDAEvD,CAAC,eAAe,KAAK,QAAQ,kBAC5B,8OAAC;wDACC,WAAW,CAAC,wDAAwD,EAClE,cAAc,QAAQ,CAAC,KAAK,IAAI,IAAI,cAAc,IAClD;wDACF,MAAK;wDACL,QAAO;wDACP,SAAQ;kEAER,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;;4CAM1E,KAAK,QAAQ,kBACZ,8OAAC;gDACC,UAAU,KAAK,QAAQ;gDACvB,YAAY,cAAc,QAAQ,CAAC,KAAK,IAAI;gDAC5C,aAAa;gDACb,UAAU;;;;;;;;;;;+CAKhB,QAAQ;kDACR,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,IAAI;wCACf,SAAS,IAAM,oBAAoB;wCACnC,WAAW,CAAC,+FAA+F,EACzG,SAAS,KAAK,IAAI,IACd,kEACA,yHACL,CAAC,EAAE,cAAc,mBAAmB,aAAa;wCAClD,OAAO,cAAc,KAAK,IAAI,GAAG;;0DAEjC,8OAAC;gDAAK,WAAU;0DAAiB,KAAK,IAAI;;;;;;4CACzC,CAAC,6BAAe,8OAAC;gDAAK,WAAU;0DAAY,KAAK,IAAI;;;;;;;;;;;;mCAvDnD,KAAK,IAAI;;;;;;;;;;;;;;;kCAgExB,8OAAC;wBAAI,WAAU;kCACZ,mBAAmB,qBAClB,8OAAC;4BAAI,WAAW,CAAC,8CAA8C,EAAE,cAAc,mBAAmB,aAAa;;8CAC7G,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDACb,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;gCAGnC,CAAC,6BACA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDACV,KAAK,IAAI;;;;;;sDAEZ,8OAAC;4CAAE,WAAU;sDACV,KAAK,KAAK;;;;;;;;;;;;gCAIhB,CAAC,6BACA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS;wCACT,WAAU;wCACV,OAAM;kDAEN,cAAA,8OAAC;4CAAI,WAAU;4CAA2C,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAClG,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;iDAO/E,8OAAC;4BAAI,WAAW,CAAC,8CAA8C,EAAE,cAAc,mBAAmB,aAAa;sCAC5G,CAAC,4BACA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;qDAID,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,OAAM;0CAEN,cAAA,8OAAC;oCAAI,WAAU;oCAA2C,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAClG,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUlF,kCACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,oBAAoB;;;;;;;;AAK7C", "debugId": null}}]}
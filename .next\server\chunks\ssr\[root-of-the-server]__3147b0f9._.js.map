{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/src/components/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\n\ninterface MenuItem {\n  name: string;\n  href: string;\n  icon: React.ReactNode;\n  children?: MenuItem[];\n}\n\nconst menuItems: MenuItem[] = [\n  {\n    name: '首页',\n    href: '/',\n    icon: (\n      <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" />\n      </svg>\n    ),\n  },\n  {\n    name: '用户管理',\n    href: '/users',\n    icon: (\n      <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\" />\n      </svg>\n    ),\n    children: [\n      {\n        name: '用户列表',\n        href: '/users',\n        icon: (\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n          </svg>\n        ),\n      },\n      {\n        name: '添加用户',\n        href: '/users/add',\n        icon: (\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n          </svg>\n        ),\n      },\n    ],\n  },\n  {\n    name: '统计分析',\n    href: '/analytics',\n    icon: (\n      <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n      </svg>\n    ),\n  },\n  {\n    name: '设置',\n    href: '/settings',\n    icon: (\n      <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n      </svg>\n    ),\n  },\n];\n\nexport default function Sidebar() {\n  const pathname = usePathname();\n  const [expandedItems, setExpandedItems] = useState<string[]>(['用户管理']);\n  const [isCollapsed, setIsCollapsed] = useState(false);\n\n  const toggleExpanded = (itemName: string) => {\n    setExpandedItems(prev =>\n      prev.includes(itemName)\n        ? prev.filter(name => name !== itemName)\n        : [...prev, itemName]\n    );\n  };\n\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return pathname === '/';\n    }\n    return pathname.startsWith(href);\n  };\n\n  const isParentActive = (item: MenuItem) => {\n    if (isActive(item.href)) return true;\n    if (item.children) {\n      return item.children.some(child => isActive(child.href));\n    }\n    return false;\n  };\n\n  return (\n    <>\n      {/* 侧边栏 */}\n      <div className={`fixed left-0 top-0 h-full bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transition-all duration-300 z-30 ${\n        isCollapsed ? 'w-16' : 'w-64'\n      }`}>\n        {/* 头部 */}\n        <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\">\n          {!isCollapsed && (\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">U</span>\n              </div>\n              <span className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                用户管理系统\n              </span>\n            </div>\n          )}\n          <button\n            onClick={() => setIsCollapsed(!isCollapsed)}\n            className=\"p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n          >\n            <svg\n              className={`w-5 h-5 text-gray-500 dark:text-gray-400 transition-transform ${\n                isCollapsed ? 'rotate-180' : ''\n              }`}\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 19l-7-7 7-7m8 14l-7-7 7-7\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* 导航菜单 */}\n        <nav className=\"flex-1 overflow-y-auto p-4\">\n          <ul className=\"space-y-2\">\n            {menuItems.map((item) => (\n              <li key={item.name}>\n                {item.children ? (\n                  // 有子菜单的项目\n                  <div>\n                    <button\n                      onClick={() => !isCollapsed && toggleExpanded(item.name)}\n                      className={`w-full flex items-center justify-between px-3 py-2 rounded-md text-sm font-medium transition-colors ${\n                        isParentActive(item)\n                          ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200'\n                          : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'\n                      }`}\n                    >\n                      <div className=\"flex items-center space-x-3\">\n                        {item.icon}\n                        {!isCollapsed && <span>{item.name}</span>}\n                      </div>\n                      {!isCollapsed && item.children && (\n                        <svg\n                          className={`w-4 h-4 transition-transform ${\n                            expandedItems.includes(item.name) ? 'rotate-90' : ''\n                          }`}\n                          fill=\"none\"\n                          stroke=\"currentColor\"\n                          viewBox=\"0 0 24 24\"\n                        >\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                        </svg>\n                      )}\n                    </button>\n                    \n                    {/* 子菜单 */}\n                    {!isCollapsed && expandedItems.includes(item.name) && item.children && (\n                      <ul className=\"mt-2 ml-6 space-y-1\">\n                        {item.children.map((child) => (\n                          <li key={child.name}>\n                            <Link\n                              href={child.href}\n                              className={`flex items-center space-x-3 px-3 py-2 rounded-md text-sm transition-colors ${\n                                isActive(child.href)\n                                  ? 'bg-blue-50 text-blue-600 dark:bg-blue-900/50 dark:text-blue-300'\n                                  : 'text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700/50'\n                              }`}\n                            >\n                              {child.icon}\n                              <span>{child.name}</span>\n                            </Link>\n                          </li>\n                        ))}\n                      </ul>\n                    )}\n                  </div>\n                ) : (\n                  // 普通菜单项\n                  <Link\n                    href={item.href}\n                    className={`flex items-center space-x-3 px-3 py-2 rounded-md text-sm font-medium transition-colors ${\n                      isActive(item.href)\n                        ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200'\n                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'\n                    }`}\n                    title={isCollapsed ? item.name : undefined}\n                  >\n                    {item.icon}\n                    {!isCollapsed && <span>{item.name}</span>}\n                  </Link>\n                )}\n              </li>\n            ))}\n          </ul>\n        </nav>\n\n        {/* 底部用户信息 */}\n        <div className=\"border-t border-gray-200 dark:border-gray-700 p-4\">\n          <div className={`flex items-center ${isCollapsed ? 'justify-center' : 'space-x-3'}`}>\n            <div className=\"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center\">\n              <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">A</span>\n            </div>\n            {!isCollapsed && (\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium text-gray-900 dark:text-white truncate\">\n                  管理员\n                </p>\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\n                  <EMAIL>\n                </p>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* 移动端遮罩 */}\n      {!isCollapsed && (\n        <div \n          className=\"fixed inset-0 bg-black bg-opacity-50 z-20 lg:hidden\"\n          onClick={() => setIsCollapsed(true)}\n        />\n      )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAaA,MAAM,YAAwB;IAC5B;QACE,MAAM;QACN,MAAM;QACN,oBACE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;IAG3E;IACA;QACE,MAAM;QACN,MAAM;QACN,oBACE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;QAGzE,UAAU;YACR;gBACE,MAAM;gBACN,MAAM;gBACN,oBACE,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAG3E;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,oBACE,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAG3E;SACD;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,oBACE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;IAG3E;IACA;QACE,MAAM;QACN,MAAM;QACN,oBACE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;;8BACjE,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;8BACrE,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;;IAG3E;CACD;AAEc,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;KAAO;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,iBAAiB,CAAC;QACtB,iBAAiB,CAAA,OACf,KAAK,QAAQ,CAAC,YACV,KAAK,MAAM,CAAC,CAAA,OAAQ,SAAS,YAC7B;mBAAI;gBAAM;aAAS;IAE3B;IAEA,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,SAAS,KAAK,IAAI,GAAG,OAAO;QAChC,IAAI,KAAK,QAAQ,EAAE;YACjB,OAAO,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAA,QAAS,SAAS,MAAM,IAAI;QACxD;QACA,OAAO;IACT;IAEA,qBACE;;0BAEE,8OAAC;gBAAI,WAAW,CAAC,mIAAmI,EAClJ,cAAc,SAAS,QACvB;;kCAEA,8OAAC;wBAAI,WAAU;;4BACZ,CAAC,6BACA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,8OAAC;wCAAK,WAAU;kDAAsD;;;;;;;;;;;;0CAK1E,8OAAC;gCACC,SAAS,IAAM,eAAe,CAAC;gCAC/B,WAAU;0CAEV,cAAA,8OAAC;oCACC,WAAW,CAAC,8DAA8D,EACxE,cAAc,eAAe,IAC7B;oCACF,MAAK;oCACL,QAAO;oCACP,SAAQ;8CAER,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;kCAM3E,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCACX,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC;8CACE,KAAK,QAAQ,GACZ,UAAU;kDACV,8OAAC;;0DACC,8OAAC;gDACC,SAAS,IAAM,CAAC,eAAe,eAAe,KAAK,IAAI;gDACvD,WAAW,CAAC,oGAAoG,EAC9G,eAAe,QACX,kEACA,6EACJ;;kEAEF,8OAAC;wDAAI,WAAU;;4DACZ,KAAK,IAAI;4DACT,CAAC,6BAAe,8OAAC;0EAAM,KAAK,IAAI;;;;;;;;;;;;oDAElC,CAAC,eAAe,KAAK,QAAQ,kBAC5B,8OAAC;wDACC,WAAW,CAAC,6BAA6B,EACvC,cAAc,QAAQ,CAAC,KAAK,IAAI,IAAI,cAAc,IAClD;wDACF,MAAK;wDACL,QAAO;wDACP,SAAQ;kEAER,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;;4CAM1E,CAAC,eAAe,cAAc,QAAQ,CAAC,KAAK,IAAI,KAAK,KAAK,QAAQ,kBACjE,8OAAC;gDAAG,WAAU;0DACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,sBAClB,8OAAC;kEACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,MAAM,IAAI;4DAChB,WAAW,CAAC,2EAA2E,EACrF,SAAS,MAAM,IAAI,IACf,oEACA,+EACJ;;gEAED,MAAM,IAAI;8EACX,8OAAC;8EAAM,MAAM,IAAI;;;;;;;;;;;;uDAVZ,MAAM,IAAI;;;;;;;;;;;;;;;+CAkB3B,QAAQ;kDACR,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC,uFAAuF,EACjG,SAAS,KAAK,IAAI,IACd,kEACA,6EACJ;wCACF,OAAO,cAAc,KAAK,IAAI,GAAG;;4CAEhC,KAAK,IAAI;4CACT,CAAC,6BAAe,8OAAC;0DAAM,KAAK,IAAI;;;;;;;;;;;;mCA/D9B,KAAK,IAAI;;;;;;;;;;;;;;;kCAwExB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAW,CAAC,kBAAkB,EAAE,cAAc,mBAAmB,aAAa;;8CACjF,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAAuD;;;;;;;;;;;gCAExE,CAAC,6BACA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAA6D;;;;;;sDAG1E,8OAAC;4CAAE,WAAU;sDAAoD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAU1E,CAAC,6BACA,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;;;AAKxC", "debugId": null}}]}
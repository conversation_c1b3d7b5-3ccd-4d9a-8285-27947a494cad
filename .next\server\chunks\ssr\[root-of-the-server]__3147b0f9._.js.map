{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/src/components/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\n\ninterface MenuItem {\n  name: string;\n  href: string;\n  icon: React.ReactNode;\n  children?: MenuItem[];\n}\n\nconst menuItems: MenuItem[] = [\n  {\n    name: '首页',\n    href: '/',\n    icon: (\n      <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" />\n      </svg>\n    ),\n  },\n  {\n    name: '用户管理',\n    href: '/users',\n    icon: (\n      <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\" />\n      </svg>\n    ),\n    children: [\n      {\n        name: '用户列表',\n        href: '/users',\n        icon: (\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n          </svg>\n        ),\n      },\n      {\n        name: '添加用户',\n        href: '/users/add',\n        icon: (\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n          </svg>\n        ),\n      },\n    ],\n  },\n  {\n    name: '统计分析',\n    href: '/analytics',\n    icon: (\n      <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n      </svg>\n    ),\n  },\n  {\n    name: '设置',\n    href: '/settings',\n    icon: (\n      <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n      </svg>\n    ),\n  },\n];\n\n// 子菜单组件，带有平滑动画\ninterface SubMenuProps {\n  children: MenuItem[];\n  isExpanded: boolean;\n  isCollapsed: boolean;\n  isActive: (href: string) => boolean;\n}\n\nfunction SubMenu({ children, isExpanded, isCollapsed, isActive }: SubMenuProps) {\n  const contentRef = useRef<HTMLDivElement>(null);\n  const [height, setHeight] = useState(0);\n\n  useEffect(() => {\n    if (contentRef.current) {\n      setHeight(isExpanded && !isCollapsed ? contentRef.current.scrollHeight : 0);\n    }\n  }, [isExpanded, isCollapsed]);\n\n  return (\n    <div\n      className=\"overflow-hidden transition-all duration-300 ease-in-out\"\n      style={{ height: `${height}px` }}\n    >\n      <div ref={contentRef} className=\"mt-2 ml-6 space-y-1\">\n        {children.map((child) => (\n          <Link\n            key={child.name}\n            href={child.href}\n            className={`flex items-center space-x-3 px-3 py-2 rounded-md text-sm transition-all duration-200 ${\n              isActive(child.href)\n                ? 'bg-blue-50 text-blue-600 dark:bg-blue-900/50 dark:text-blue-300 border-l-2 border-blue-600'\n                : 'text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-gray-200'\n            }`}\n          >\n            {child.icon}\n            <span>{child.name}</span>\n          </Link>\n        ))}\n      </div>\n    </div>\n  );\n}\n\nexport default function Sidebar() {\n  const pathname = usePathname();\n  const [expandedItems, setExpandedItems] = useState<string[]>(['用户管理']);\n  const [isCollapsed, setIsCollapsed] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  // 定义isActive函数（需要在useEffect之前定义）\n  const isActive = (href: string) => {\n    // 首页特殊处理\n    if (href === '/') {\n      return pathname === '/';\n    }\n\n    // 精确匹配\n    if (pathname === href) {\n      return true;\n    }\n\n    // 对于子路径，确保是完整的路径段匹配\n    // 例如：/users/add 应该匹配 /users，但 /users-admin 不应该匹配 /users\n    if (pathname.startsWith(href + '/')) {\n      return true;\n    }\n\n    return false;\n  };\n\n  // 自动展开包含当前页面的菜单项\n  useEffect(() => {\n    if (!isCollapsed) {\n      const itemsToExpand: string[] = [];\n      menuItems.forEach(item => {\n        if (item.children) {\n          const hasActiveChild = item.children.some(child => isActive(child.href));\n          if (hasActiveChild) {\n            itemsToExpand.push(item.name);\n          }\n        }\n      });\n\n      // 只有当需要展开的项目发生变化时才更新状态\n      setExpandedItems(prev => {\n        const newItems = [...new Set([...prev, ...itemsToExpand])];\n        return newItems.length !== prev.length || newItems.some(item => !prev.includes(item))\n          ? newItems\n          : prev;\n      });\n    }\n  }, [pathname, isCollapsed]);\n\n  // 当侧边栏收起时，关闭所有展开的子菜单\n  useEffect(() => {\n    if (isCollapsed) {\n      setExpandedItems([]);\n    }\n  }, [isCollapsed]);\n\n  const toggleExpanded = (itemName: string) => {\n    if (isCollapsed) return; // 收起状态下不允许展开子菜单\n\n    setExpandedItems(prev =>\n      prev.includes(itemName)\n        ? prev.filter(name => name !== itemName)\n        : [...prev, itemName]\n    );\n  };\n\n\n\n  const isParentActive = (item: MenuItem) => {\n    // 如果有子菜单，检查是否有子菜单项被激活\n    if (item.children) {\n      const hasActiveChild = item.children.some(child => isActive(child.href));\n      if (hasActiveChild) {\n        return true;\n      }\n    }\n\n    // 检查父菜单自身是否激活（对于没有子菜单的项目，或者直接访问父菜单路径的情况）\n    return isActive(item.href);\n  };\n\n  const toggleSidebar = () => {\n    setIsCollapsed(!isCollapsed);\n    setIsMobileMenuOpen(false);\n  };\n\n  const toggleMobileMenu = () => {\n    setIsMobileMenuOpen(!isMobileMenuOpen);\n  };\n\n  return (\n    <>\n      {/* 移动端菜单按钮 */}\n      <button\n        onClick={toggleMobileMenu}\n        className=\"fixed top-4 left-4 z-50 p-2 bg-white dark:bg-gray-800 rounded-md shadow-md lg:hidden\"\n      >\n        <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n        </svg>\n      </button>\n\n      {/* 侧边栏 */}\n      <div className={`fixed left-0 top-0 h-full bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transition-all duration-300 ease-in-out z-40 ${\n        isCollapsed ? 'w-16' : 'w-64'\n      } ${\n        isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'\n      }`}>\n        {/* 头部 */}\n        <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\">\n          <div className={`flex items-center transition-all duration-300 ${isCollapsed ? 'justify-center w-full' : 'space-x-2'}`}>\n            <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0\">\n              <span className=\"text-white font-bold text-sm\">U</span>\n            </div>\n            {!isCollapsed && (\n              <span className=\"text-lg font-semibold text-gray-900 dark:text-white whitespace-nowrap overflow-hidden\">\n                用户管理系统\n              </span>\n            )}\n          </div>\n          {!isCollapsed && (\n            <button\n              onClick={toggleSidebar}\n              className=\"p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex-shrink-0 hidden lg:block\"\n              title=\"收起侧边栏\"\n            >\n              <svg\n                className=\"w-5 h-5 text-gray-500 dark:text-gray-400 transition-transform duration-300\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 19l-7-7 7-7\" />\n              </svg>\n            </button>\n          )}\n        </div>\n\n        {/* 收起状态下的展开按钮 */}\n        {isCollapsed && (\n          <div className=\"p-4 border-b border-gray-200 dark:border-gray-700\">\n            <button\n              onClick={toggleSidebar}\n              className=\"w-full p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n              title=\"展开侧边栏\"\n            >\n              <svg\n                className=\"w-5 h-5 text-gray-500 dark:text-gray-400 transition-transform duration-300 mx-auto\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 5l7 7-7 7\" />\n              </svg>\n            </button>\n          </div>\n        )}\n\n        {/* 导航菜单 */}\n        <nav className=\"flex-1 overflow-y-auto p-4\">\n          <ul className=\"space-y-1\">\n            {menuItems.map((item) => (\n              <li key={item.name}>\n                {item.children ? (\n                  // 有子菜单的项目\n                  <div>\n                    <button\n                      onClick={() => toggleExpanded(item.name)}\n                      disabled={isCollapsed}\n                      className={`w-full flex items-center justify-between px-3 py-2.5 rounded-lg text-sm font-medium transition-all duration-200 group ${\n                        isParentActive(item)\n                          ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200'\n                          : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'\n                      } ${isCollapsed ? 'justify-center' : ''}`}\n                      title={isCollapsed ? item.name : undefined}\n                    >\n                      <div className={`flex items-center ${isCollapsed ? 'justify-center' : 'space-x-3'}`}>\n                        <span className=\"flex-shrink-0\">{item.icon}</span>\n                        {!isCollapsed && <span className=\"truncate\">{item.name}</span>}\n                      </div>\n                      {!isCollapsed && item.children && (\n                        <svg\n                          className={`w-4 h-4 transition-transform duration-300 flex-shrink-0 ${\n                            expandedItems.includes(item.name) ? 'rotate-90' : ''\n                          }`}\n                          fill=\"none\"\n                          stroke=\"currentColor\"\n                          viewBox=\"0 0 24 24\"\n                        >\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                        </svg>\n                      )}\n                    </button>\n\n                    {/* 子菜单 */}\n                    {item.children && (\n                      <SubMenu\n                        children={item.children}\n                        isExpanded={expandedItems.includes(item.name)}\n                        isCollapsed={isCollapsed}\n                        isActive={isActive}\n                      />\n                    )}\n                  </div>\n                ) : (\n                  // 普通菜单项\n                  <Link\n                    href={item.href}\n                    onClick={() => setIsMobileMenuOpen(false)}\n                    className={`flex items-center px-3 py-2.5 rounded-lg text-sm font-medium transition-all duration-200 group ${\n                      isActive(item.href)\n                        ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200'\n                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'\n                    } ${isCollapsed ? 'justify-center' : 'space-x-3'}`}\n                    title={isCollapsed ? item.name : undefined}\n                  >\n                    <span className=\"flex-shrink-0\">{item.icon}</span>\n                    {!isCollapsed && <span className=\"truncate\">{item.name}</span>}\n                  </Link>\n                )}\n              </li>\n            ))}\n          </ul>\n        </nav>\n\n        {/* 底部用户信息 */}\n        <div className=\"border-t border-gray-200 dark:border-gray-700 p-4\">\n          <div className={`flex items-center transition-all duration-300 ${isCollapsed ? 'justify-center' : 'space-x-3'}`}>\n            <div className=\"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0\">\n              <span className=\"text-sm font-medium text-white\">A</span>\n            </div>\n            {!isCollapsed && (\n              <div className=\"flex-1 min-w-0 opacity-100 transition-opacity duration-300\">\n                <p className=\"text-sm font-medium text-gray-900 dark:text-white truncate\">\n                  管理员\n                </p>\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\n                  <EMAIL>\n                </p>\n              </div>\n            )}\n            {!isCollapsed && (\n              <button\n                className=\"p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n                title=\"用户设置\"\n              >\n                <svg className=\"w-4 h-4 text-gray-500 dark:text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z\" />\n                </svg>\n              </button>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* 移动端遮罩 */}\n      {isMobileMenuOpen && (\n        <div\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden transition-opacity duration-300\"\n          onClick={() => setIsMobileMenuOpen(false)}\n        />\n      )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAaA,MAAM,YAAwB;IAC5B;QACE,MAAM;QACN,MAAM;QACN,oBACE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;IAG3E;IACA;QACE,MAAM;QACN,MAAM;QACN,oBACE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;QAGzE,UAAU;YACR;gBACE,MAAM;gBACN,MAAM;gBACN,oBACE,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAG3E;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,oBACE,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAG3E;SACD;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,oBACE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;IAG3E;IACA;QACE,MAAM;QACN,MAAM;QACN,oBACE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;;8BACjE,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;8BACrE,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;;IAG3E;CACD;AAUD,SAAS,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAgB;IAC5E,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,OAAO,EAAE;YACtB,UAAU,cAAc,CAAC,cAAc,WAAW,OAAO,CAAC,YAAY,GAAG;QAC3E;IACF,GAAG;QAAC;QAAY;KAAY;IAE5B,qBACE,8OAAC;QACC,WAAU;QACV,OAAO;YAAE,QAAQ,GAAG,OAAO,EAAE,CAAC;QAAC;kBAE/B,cAAA,8OAAC;YAAI,KAAK;YAAY,WAAU;sBAC7B,SAAS,GAAG,CAAC,CAAC,sBACb,8OAAC,4JAAA,CAAA,UAAI;oBAEH,MAAM,MAAM,IAAI;oBAChB,WAAW,CAAC,qFAAqF,EAC/F,SAAS,MAAM,IAAI,IACf,+FACA,4HACJ;;wBAED,MAAM,IAAI;sCACX,8OAAC;sCAAM,MAAM,IAAI;;;;;;;mBATZ,MAAM,IAAI;;;;;;;;;;;;;;;AAe3B;AAEe,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;KAAO;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,iCAAiC;IACjC,MAAM,WAAW,CAAC;QAChB,SAAS;QACT,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QAEA,OAAO;QACP,IAAI,aAAa,MAAM;YACrB,OAAO;QACT;QAEA,oBAAoB;QACpB,wDAAwD;QACxD,IAAI,SAAS,UAAU,CAAC,OAAO,MAAM;YACnC,OAAO;QACT;QAEA,OAAO;IACT;IAEA,iBAAiB;IACjB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa;YAChB,MAAM,gBAA0B,EAAE;YAClC,UAAU,OAAO,CAAC,CAAA;gBAChB,IAAI,KAAK,QAAQ,EAAE;oBACjB,MAAM,iBAAiB,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAA,QAAS,SAAS,MAAM,IAAI;oBACtE,IAAI,gBAAgB;wBAClB,cAAc,IAAI,CAAC,KAAK,IAAI;oBAC9B;gBACF;YACF;YAEA,uBAAuB;YACvB,iBAAiB,CAAA;gBACf,MAAM,WAAW;uBAAI,IAAI,IAAI;2BAAI;2BAAS;qBAAc;iBAAE;gBAC1D,OAAO,SAAS,MAAM,KAAK,KAAK,MAAM,IAAI,SAAS,IAAI,CAAC,CAAA,OAAQ,CAAC,KAAK,QAAQ,CAAC,SAC3E,WACA;YACN;QACF;IACF,GAAG;QAAC;QAAU;KAAY;IAE1B,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa;YACf,iBAAiB,EAAE;QACrB;IACF,GAAG;QAAC;KAAY;IAEhB,MAAM,iBAAiB,CAAC;QACtB,IAAI,aAAa,QAAQ,gBAAgB;QAEzC,iBAAiB,CAAA,OACf,KAAK,QAAQ,CAAC,YACV,KAAK,MAAM,CAAC,CAAA,OAAQ,SAAS,YAC7B;mBAAI;gBAAM;aAAS;IAE3B;IAIA,MAAM,iBAAiB,CAAC;QACtB,sBAAsB;QACtB,IAAI,KAAK,QAAQ,EAAE;YACjB,MAAM,iBAAiB,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAA,QAAS,SAAS,MAAM,IAAI;YACtE,IAAI,gBAAgB;gBAClB,OAAO;YACT;QACF;QAEA,yCAAyC;QACzC,OAAO,SAAS,KAAK,IAAI;IAC3B;IAEA,MAAM,gBAAgB;QACpB,eAAe,CAAC;QAChB,oBAAoB;IACtB;IAEA,MAAM,mBAAmB;QACvB,oBAAoB,CAAC;IACvB;IAEA,qBACE;;0BAEE,8OAAC;gBACC,SAAS;gBACT,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;;;;;0BAKzE,8OAAC;gBAAI,WAAW,CAAC,+IAA+I,EAC9J,cAAc,SAAS,OACxB,CAAC,EACA,mBAAmB,kBAAkB,sCACrC;;kCAEA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAW,CAAC,8CAA8C,EAAE,cAAc,0BAA0B,aAAa;;kDACpH,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;oCAEhD,CAAC,6BACA,8OAAC;wCAAK,WAAU;kDAAwF;;;;;;;;;;;;4BAK3G,CAAC,6BACA,8OAAC;gCACC,SAAS;gCACT,WAAU;gCACV,OAAM;0CAEN,cAAA,8OAAC;oCACC,WAAU;oCACV,MAAK;oCACL,QAAO;oCACP,SAAQ;8CAER,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;oBAO5E,6BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS;4BACT,WAAU;4BACV,OAAM;sCAEN,cAAA,8OAAC;gCACC,WAAU;gCACV,MAAK;gCACL,QAAO;gCACP,SAAQ;0CAER,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;kCAO7E,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCACX,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC;8CACE,KAAK,QAAQ,GACZ,UAAU;kDACV,8OAAC;;0DACC,8OAAC;gDACC,SAAS,IAAM,eAAe,KAAK,IAAI;gDACvC,UAAU;gDACV,WAAW,CAAC,sHAAsH,EAChI,eAAe,QACX,kEACA,yHACL,CAAC,EAAE,cAAc,mBAAmB,IAAI;gDACzC,OAAO,cAAc,KAAK,IAAI,GAAG;;kEAEjC,8OAAC;wDAAI,WAAW,CAAC,kBAAkB,EAAE,cAAc,mBAAmB,aAAa;;0EACjF,8OAAC;gEAAK,WAAU;0EAAiB,KAAK,IAAI;;;;;;4DACzC,CAAC,6BAAe,8OAAC;gEAAK,WAAU;0EAAY,KAAK,IAAI;;;;;;;;;;;;oDAEvD,CAAC,eAAe,KAAK,QAAQ,kBAC5B,8OAAC;wDACC,WAAW,CAAC,wDAAwD,EAClE,cAAc,QAAQ,CAAC,KAAK,IAAI,IAAI,cAAc,IAClD;wDACF,MAAK;wDACL,QAAO;wDACP,SAAQ;kEAER,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;;4CAM1E,KAAK,QAAQ,kBACZ,8OAAC;gDACC,UAAU,KAAK,QAAQ;gDACvB,YAAY,cAAc,QAAQ,CAAC,KAAK,IAAI;gDAC5C,aAAa;gDACb,UAAU;;;;;;;;;;;+CAKhB,QAAQ;kDACR,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,IAAI;wCACf,SAAS,IAAM,oBAAoB;wCACnC,WAAW,CAAC,+FAA+F,EACzG,SAAS,KAAK,IAAI,IACd,kEACA,yHACL,CAAC,EAAE,cAAc,mBAAmB,aAAa;wCAClD,OAAO,cAAc,KAAK,IAAI,GAAG;;0DAEjC,8OAAC;gDAAK,WAAU;0DAAiB,KAAK,IAAI;;;;;;4CACzC,CAAC,6BAAe,8OAAC;gDAAK,WAAU;0DAAY,KAAK,IAAI;;;;;;;;;;;;mCAvDnD,KAAK,IAAI;;;;;;;;;;;;;;;kCAgExB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAW,CAAC,8CAA8C,EAAE,cAAc,mBAAmB,aAAa;;8CAC7G,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAAiC;;;;;;;;;;;gCAElD,CAAC,6BACA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAA6D;;;;;;sDAG1E,8OAAC;4CAAE,WAAU;sDAAoD;;;;;;;;;;;;gCAKpE,CAAC,6BACA,8OAAC;oCACC,WAAU;oCACV,OAAM;8CAEN,cAAA,8OAAC;wCAAI,WAAU;wCAA2C,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAClG,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAShF,kCACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,oBAAoB;;;;;;;;AAK7C", "debugId": null}}]}
export interface User {
  id: string;
  name: string;
  email: string;
  phone?: string;
  role: 'admin' | 'user' | 'moderator';
  status: 'active' | 'inactive' | 'pending';
  avatar?: string;
  createdAt: string;
  updatedAt: string;
  department?: string;
  position?: string;
}

export interface CreateUserData {
  name: string;
  email: string;
  phone?: string;
  role: 'admin' | 'user' | 'moderator';
  status: 'active' | 'inactive' | 'pending';
  department?: string;
  position?: string;
}

export interface UpdateUserData extends Partial<CreateUserData> {
  id: string;
}

export interface UserFilters {
  search?: string;
  role?: string;
  status?: string;
  department?: string;
}

export interface PaginationParams {
  page: number;
  limit: number;
}

export interface UserListResponse {
  users: User[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/src/lib/users.ts"], "sourcesContent": ["import { User, CreateUserData, UpdateUserData, UserFilters, PaginationParams, UserListResponse } from '@/types/user';\n\n// 模拟用户数据存储\nlet users: User[] = [\n  {\n    id: '1',\n    name: '张三',\n    email: '<PERSON><PERSON><PERSON>@example.com',\n    phone: '13800138001',\n    role: 'admin',\n    status: 'active',\n    department: '技术部',\n    position: '高级工程师',\n    createdAt: '2024-01-15T08:00:00Z',\n    updatedAt: '2024-01-15T08:00:00Z',\n  },\n  {\n    id: '2',\n    name: '李四',\n    email: '<EMAIL>',\n    phone: '13800138002',\n    role: 'user',\n    status: 'active',\n    department: '产品部',\n    position: '产品经理',\n    createdAt: '2024-01-16T09:00:00Z',\n    updatedAt: '2024-01-16T09:00:00Z',\n  },\n  {\n    id: '3',\n    name: '王五',\n    email: '<EMAIL>',\n    phone: '13800138003',\n    role: 'moderator',\n    status: 'pending',\n    department: '运营部',\n    position: '运营专员',\n    createdAt: '2024-01-17T10:00:00Z',\n    updatedAt: '2024-01-17T10:00:00Z',\n  },\n];\n\n// 生成唯一ID\nfunction generateId(): string {\n  return Date.now().toString() + Math.random().toString(36).substr(2, 9);\n}\n\n// 获取用户列表\nexport function getUsers(filters?: UserFilters, pagination?: PaginationParams): UserListResponse {\n  let filteredUsers = [...users];\n\n  // 应用筛选\n  if (filters) {\n    if (filters.search) {\n      const searchLower = filters.search.toLowerCase();\n      filteredUsers = filteredUsers.filter(user =>\n        user.name.toLowerCase().includes(searchLower) ||\n        user.email.toLowerCase().includes(searchLower) ||\n        user.phone?.toLowerCase().includes(searchLower)\n      );\n    }\n\n    if (filters.role) {\n      filteredUsers = filteredUsers.filter(user => user.role === filters.role);\n    }\n\n    if (filters.status) {\n      filteredUsers = filteredUsers.filter(user => user.status === filters.status);\n    }\n\n    if (filters.department) {\n      filteredUsers = filteredUsers.filter(user => user.department === filters.department);\n    }\n  }\n\n  const total = filteredUsers.length;\n\n  // 应用分页\n  if (pagination) {\n    const startIndex = (pagination.page - 1) * pagination.limit;\n    const endIndex = startIndex + pagination.limit;\n    filteredUsers = filteredUsers.slice(startIndex, endIndex);\n  }\n\n  return {\n    users: filteredUsers,\n    total,\n    page: pagination?.page || 1,\n    limit: pagination?.limit || total,\n    totalPages: pagination ? Math.ceil(total / pagination.limit) : 1,\n  };\n}\n\n// 根据ID获取用户\nexport function getUserById(id: string): User | null {\n  return users.find(user => user.id === id) || null;\n}\n\n// 创建用户\nexport function createUser(userData: CreateUserData): User {\n  // 检查邮箱是否已存在\n  const existingUser = users.find(user => user.email === userData.email);\n  if (existingUser) {\n    throw new Error('邮箱已存在');\n  }\n\n  const now = new Date().toISOString();\n  const newUser: User = {\n    id: generateId(),\n    ...userData,\n    createdAt: now,\n    updatedAt: now,\n  };\n\n  users.push(newUser);\n  return newUser;\n}\n\n// 更新用户\nexport function updateUser(userData: UpdateUserData): User {\n  const userIndex = users.findIndex(user => user.id === userData.id);\n  if (userIndex === -1) {\n    throw new Error('用户不存在');\n  }\n\n  // 检查邮箱是否被其他用户使用\n  if (userData.email) {\n    const existingUser = users.find(user => user.email === userData.email && user.id !== userData.id);\n    if (existingUser) {\n      throw new Error('邮箱已被其他用户使用');\n    }\n  }\n\n  const updatedUser: User = {\n    ...users[userIndex],\n    ...userData,\n    updatedAt: new Date().toISOString(),\n  };\n\n  users[userIndex] = updatedUser;\n  return updatedUser;\n}\n\n// 删除用户\nexport function deleteUser(id: string): boolean {\n  const userIndex = users.findIndex(user => user.id === id);\n  if (userIndex === -1) {\n    return false;\n  }\n\n  users.splice(userIndex, 1);\n  return true;\n}\n\n// 获取统计信息\nexport function getUserStats() {\n  const total = users.length;\n  const active = users.filter(user => user.status === 'active').length;\n  const inactive = users.filter(user => user.status === 'inactive').length;\n  const pending = users.filter(user => user.status === 'pending').length;\n\n  const roleStats = {\n    admin: users.filter(user => user.role === 'admin').length,\n    user: users.filter(user => user.role === 'user').length,\n    moderator: users.filter(user => user.role === 'moderator').length,\n  };\n\n  return {\n    total,\n    active,\n    inactive,\n    pending,\n    roleStats,\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA,WAAW;AACX,IAAI,QAAgB;IAClB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,UAAU;QACV,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,UAAU;QACV,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,UAAU;QACV,WAAW;QACX,WAAW;IACb;CACD;AAED,SAAS;AACT,SAAS;IACP,OAAO,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AACtE;AAGO,SAAS,SAAS,OAAqB,EAAE,UAA6B;IAC3E,IAAI,gBAAgB;WAAI;KAAM;IAE9B,OAAO;IACP,IAAI,SAAS;QACX,IAAI,QAAQ,MAAM,EAAE;YAClB,MAAM,cAAc,QAAQ,MAAM,CAAC,WAAW;YAC9C,gBAAgB,cAAc,MAAM,CAAC,CAAA,OACnC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACjC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAClC,KAAK,KAAK,EAAE,cAAc,SAAS;QAEvC;QAEA,IAAI,QAAQ,IAAI,EAAE;YAChB,gBAAgB,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,QAAQ,IAAI;QACzE;QAEA,IAAI,QAAQ,MAAM,EAAE;YAClB,gBAAgB,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,QAAQ,MAAM;QAC7E;QAEA,IAAI,QAAQ,UAAU,EAAE;YACtB,gBAAgB,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,UAAU,KAAK,QAAQ,UAAU;QACrF;IACF;IAEA,MAAM,QAAQ,cAAc,MAAM;IAElC,OAAO;IACP,IAAI,YAAY;QACd,MAAM,aAAa,CAAC,WAAW,IAAI,GAAG,CAAC,IAAI,WAAW,KAAK;QAC3D,MAAM,WAAW,aAAa,WAAW,KAAK;QAC9C,gBAAgB,cAAc,KAAK,CAAC,YAAY;IAClD;IAEA,OAAO;QACL,OAAO;QACP;QACA,MAAM,YAAY,QAAQ;QAC1B,OAAO,YAAY,SAAS;QAC5B,YAAY,aAAa,KAAK,IAAI,CAAC,QAAQ,WAAW,KAAK,IAAI;IACjE;AACF;AAGO,SAAS,YAAY,EAAU;IACpC,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO;AAC/C;AAGO,SAAS,WAAW,QAAwB;IACjD,YAAY;IACZ,MAAM,eAAe,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK,SAAS,KAAK;IACrE,IAAI,cAAc;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,MAAM,IAAI,OAAO,WAAW;IAClC,MAAM,UAAgB;QACpB,IAAI;QACJ,GAAG,QAAQ;QACX,WAAW;QACX,WAAW;IACb;IAEA,MAAM,IAAI,CAAC;IACX,OAAO;AACT;AAGO,SAAS,WAAW,QAAwB;IACjD,MAAM,YAAY,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,SAAS,EAAE;IACjE,IAAI,cAAc,CAAC,GAAG;QACpB,MAAM,IAAI,MAAM;IAClB;IAEA,gBAAgB;IAChB,IAAI,SAAS,KAAK,EAAE;QAClB,MAAM,eAAe,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK,SAAS,KAAK,IAAI,KAAK,EAAE,KAAK,SAAS,EAAE;QAChG,IAAI,cAAc;YAChB,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAM,cAAoB;QACxB,GAAG,KAAK,CAAC,UAAU;QACnB,GAAG,QAAQ;QACX,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,KAAK,CAAC,UAAU,GAAG;IACnB,OAAO;AACT;AAGO,SAAS,WAAW,EAAU;IACnC,MAAM,YAAY,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IACtD,IAAI,cAAc,CAAC,GAAG;QACpB,OAAO;IACT;IAEA,MAAM,MAAM,CAAC,WAAW;IACxB,OAAO;AACT;AAGO,SAAS;IACd,MAAM,QAAQ,MAAM,MAAM;IAC1B,MAAM,SAAS,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,UAAU,MAAM;IACpE,MAAM,WAAW,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,YAAY,MAAM;IACxE,MAAM,UAAU,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,WAAW,MAAM;IAEtE,MAAM,YAAY;QAChB,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,SAAS,MAAM;QACzD,MAAM,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,QAAQ,MAAM;QACvD,WAAW,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,aAAa,MAAM;IACnE;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/src/app/users/add/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { CreateUserData } from '@/types/user';\nimport { createUser } from '@/lib/users';\n\nexport default function AddUserPage() {\n  const router = useRouter();\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [formData, setFormData] = useState<CreateUserData>({\n    name: '',\n    email: '',\n    phone: '',\n    role: 'user',\n    status: 'active',\n    department: '',\n    position: '',\n  });\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value,\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    setError(null);\n\n    try {\n      // 验证必填字段\n      if (!formData.name.trim()) {\n        throw new Error('姓名不能为空');\n      }\n      if (!formData.email.trim()) {\n        throw new Error('邮箱不能为空');\n      }\n\n      // 验证邮箱格式\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      if (!emailRegex.test(formData.email)) {\n        throw new Error('邮箱格式不正确');\n      }\n\n      // 验证手机号格式（如果提供）\n      if (formData.phone && formData.phone.trim()) {\n        const phoneRegex = /^1[3-9]\\d{9}$/;\n        if (!phoneRegex.test(formData.phone.trim())) {\n          throw new Error('手机号格式不正确');\n        }\n      }\n\n      // 创建用户\n      const userData: CreateUserData = {\n        ...formData,\n        name: formData.name.trim(),\n        email: formData.email.trim(),\n        phone: formData.phone?.trim() || undefined,\n        department: formData.department?.trim() || undefined,\n        position: formData.position?.trim() || undefined,\n      };\n\n      createUser(userData);\n      \n      // 成功后跳转到用户列表\n      router.push('/users?success=created');\n    } catch (err) {\n      setError(err instanceof Error ? err.message : '创建用户失败');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* 页面标题和导航 */}\n        <div className=\"mb-8\">\n          <nav className=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-4\">\n            <Link href=\"/\" className=\"hover:text-gray-700 dark:hover:text-gray-200\">\n              首页\n            </Link>\n            <span>/</span>\n            <Link href=\"/users\" className=\"hover:text-gray-700 dark:hover:text-gray-200\">\n              用户管理\n            </Link>\n            <span>/</span>\n            <span className=\"text-gray-900 dark:text-white\">添加用户</span>\n          </nav>\n          \n          <div className=\"flex items-center justify-between\">\n            <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n              添加新用户\n            </h1>\n            <Link\n              href=\"/users\"\n              className=\"px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-colors\"\n            >\n              返回列表\n            </Link>\n          </div>\n        </div>\n\n        {/* 表单 */}\n        <div className=\"max-w-2xl mx-auto\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\">\n            {error && (\n              <div className=\"mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md\">\n                <p className=\"text-red-600 dark:text-red-400\">{error}</p>\n              </div>\n            )}\n\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              {/* 基本信息 */}\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n                  基本信息\n                </h3>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      姓名 <span className=\"text-red-500\">*</span>\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"name\"\n                      name=\"name\"\n                      value={formData.name}\n                      onChange={handleInputChange}\n                      required\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n                      placeholder=\"请输入姓名\"\n                    />\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      邮箱 <span className=\"text-red-500\">*</span>\n                    </label>\n                    <input\n                      type=\"email\"\n                      id=\"email\"\n                      name=\"email\"\n                      value={formData.email}\n                      onChange={handleInputChange}\n                      required\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n                      placeholder=\"请输入邮箱地址\"\n                    />\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      手机号\n                    </label>\n                    <input\n                      type=\"tel\"\n                      id=\"phone\"\n                      name=\"phone\"\n                      value={formData.phone}\n                      onChange={handleInputChange}\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n                      placeholder=\"请输入手机号\"\n                    />\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"role\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      角色 <span className=\"text-red-500\">*</span>\n                    </label>\n                    <select\n                      id=\"role\"\n                      name=\"role\"\n                      value={formData.role}\n                      onChange={handleInputChange}\n                      required\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n                    >\n                      <option value=\"user\">普通用户</option>\n                      <option value=\"moderator\">管理员</option>\n                      <option value=\"admin\">超级管理员</option>\n                    </select>\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"status\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      状态 <span className=\"text-red-500\">*</span>\n                    </label>\n                    <select\n                      id=\"status\"\n                      name=\"status\"\n                      value={formData.status}\n                      onChange={handleInputChange}\n                      required\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n                    >\n                      <option value=\"active\">激活</option>\n                      <option value=\"inactive\">未激活</option>\n                      <option value=\"pending\">待审核</option>\n                    </select>\n                  </div>\n                </div>\n              </div>\n\n              {/* 工作信息 */}\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n                  工作信息\n                </h3>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label htmlFor=\"department\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      部门\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"department\"\n                      name=\"department\"\n                      value={formData.department}\n                      onChange={handleInputChange}\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n                      placeholder=\"请输入部门\"\n                    />\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"position\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      职位\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"position\"\n                      name=\"position\"\n                      value={formData.position}\n                      onChange={handleInputChange}\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n                      placeholder=\"请输入职位\"\n                    />\n                  </div>\n                </div>\n              </div>\n\n              {/* 提交按钮 */}\n              <div className=\"flex justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-600\">\n                <Link\n                  href=\"/users\"\n                  className=\"px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n                >\n                  取消\n                </Link>\n                <button\n                  type=\"submit\"\n                  disabled={isSubmitting}\n                  className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n                >\n                  {isSubmitting ? '创建中...' : '创建用户'}\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;;;AANA;;;;;AAQe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;QACvD,MAAM;QACN,OAAO;QACP,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,UAAU;IACZ;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAChB,SAAS;QAET,IAAI;YACF,SAAS;YACT,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;gBACzB,MAAM,IAAI,MAAM;YAClB;YACA,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;gBAC1B,MAAM,IAAI,MAAM;YAClB;YAEA,SAAS;YACT,MAAM,aAAa;YACnB,IAAI,CAAC,WAAW,IAAI,CAAC,SAAS,KAAK,GAAG;gBACpC,MAAM,IAAI,MAAM;YAClB;YAEA,gBAAgB;YAChB,IAAI,SAAS,KAAK,IAAI,SAAS,KAAK,CAAC,IAAI,IAAI;gBAC3C,MAAM,aAAa;gBACnB,IAAI,CAAC,WAAW,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,KAAK;oBAC3C,MAAM,IAAI,MAAM;gBAClB;YACF;YAEA,OAAO;YACP,MAAM,WAA2B;gBAC/B,GAAG,QAAQ;gBACX,MAAM,SAAS,IAAI,CAAC,IAAI;gBACxB,OAAO,SAAS,KAAK,CAAC,IAAI;gBAC1B,OAAO,SAAS,KAAK,EAAE,UAAU;gBACjC,YAAY,SAAS,UAAU,EAAE,UAAU;gBAC3C,UAAU,SAAS,QAAQ,EAAE,UAAU;YACzC;YAEA,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE;YAEX,aAAa;YACb,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAA+C;;;;;;8CAGxE,6LAAC;8CAAK;;;;;;8CACN,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAA+C;;;;;;8CAG7E,6LAAC;8CAAK;;;;;;8CACN,6LAAC;oCAAK,WAAU;8CAAgC;;;;;;;;;;;;sCAGlD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAmD;;;;;;8CAGjE,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;8BAOL,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,uBACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;0CAInD,6LAAC;gCAAK,UAAU;gCAAc,WAAU;;kDAEtC,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAyD;;;;;;0DAGvE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAM,SAAQ;gEAAO,WAAU;;oEAAkE;kFAC7F,6LAAC;wEAAK,WAAU;kFAAe;;;;;;;;;;;;0EAEpC,6LAAC;gEACC,MAAK;gEACL,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,IAAI;gEACpB,UAAU;gEACV,QAAQ;gEACR,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAIhB,6LAAC;;0EACC,6LAAC;gEAAM,SAAQ;gEAAQ,WAAU;;oEAAkE;kFAC9F,6LAAC;wEAAK,WAAU;kFAAe;;;;;;;;;;;;0EAEpC,6LAAC;gEACC,MAAK;gEACL,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU;gEACV,QAAQ;gEACR,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAIhB,6LAAC;;0EACC,6LAAC;gEAAM,SAAQ;gEAAQ,WAAU;0EAAkE;;;;;;0EAGnG,6LAAC;gEACC,MAAK;gEACL,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU;gEACV,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAIhB,6LAAC;;0EACC,6LAAC;gEAAM,SAAQ;gEAAO,WAAU;;oEAAkE;kFAC7F,6LAAC;wEAAK,WAAU;kFAAe;;;;;;;;;;;;0EAEpC,6LAAC;gEACC,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,IAAI;gEACpB,UAAU;gEACV,QAAQ;gEACR,WAAU;;kFAEV,6LAAC;wEAAO,OAAM;kFAAO;;;;;;kFACrB,6LAAC;wEAAO,OAAM;kFAAY;;;;;;kFAC1B,6LAAC;wEAAO,OAAM;kFAAQ;;;;;;;;;;;;;;;;;;kEAI1B,6LAAC;;0EACC,6LAAC;gEAAM,SAAQ;gEAAS,WAAU;;oEAAkE;kFAC/F,6LAAC;wEAAK,WAAU;kFAAe;;;;;;;;;;;;0EAEpC,6LAAC;gEACC,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,MAAM;gEACtB,UAAU;gEACV,QAAQ;gEACR,WAAU;;kFAEV,6LAAC;wEAAO,OAAM;kFAAS;;;;;;kFACvB,6LAAC;wEAAO,OAAM;kFAAW;;;;;;kFACzB,6LAAC;wEAAO,OAAM;kFAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOhC,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAyD;;;;;;0DAGvE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAM,SAAQ;gEAAa,WAAU;0EAAkE;;;;;;0EAGxG,6LAAC;gEACC,MAAK;gEACL,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,UAAU;gEAC1B,UAAU;gEACV,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAIhB,6LAAC;;0EACC,6LAAC;gEAAM,SAAQ;gEAAW,WAAU;0EAAkE;;;;;;0EAGtG,6LAAC;gEACC,MAAK;gEACL,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,QAAQ;gEACxB,UAAU;gEACV,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;kDAOpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,MAAK;gDACL,UAAU;gDACV,WAAU;0DAET,eAAe,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7C;GAtQwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}
import { User, CreateUserData, UpdateUserData, UserFilters, PaginationParams, UserListResponse } from '@/types/user';

// 模拟用户数据存储
let users: User[] = [
  {
    id: '1',
    name: '张三',
    email: '<PERSON><PERSON><PERSON>@example.com',
    phone: '13800138001',
    role: 'admin',
    status: 'active',
    department: '技术部',
    position: '高级工程师',
    createdAt: '2024-01-15T08:00:00Z',
    updatedAt: '2024-01-15T08:00:00Z',
  },
  {
    id: '2',
    name: '李四',
    email: '<EMAIL>',
    phone: '13800138002',
    role: 'user',
    status: 'active',
    department: '产品部',
    position: '产品经理',
    createdAt: '2024-01-16T09:00:00Z',
    updatedAt: '2024-01-16T09:00:00Z',
  },
  {
    id: '3',
    name: '王五',
    email: '<EMAIL>',
    phone: '13800138003',
    role: 'moderator',
    status: 'pending',
    department: '运营部',
    position: '运营专员',
    createdAt: '2024-01-17T10:00:00Z',
    updatedAt: '2024-01-17T10:00:00Z',
  },
];

// 生成唯一ID
function generateId(): string {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9);
}

// 获取用户列表
export function getUsers(filters?: UserFilters, pagination?: PaginationParams): UserListResponse {
  let filteredUsers = [...users];

  // 应用筛选
  if (filters) {
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filteredUsers = filteredUsers.filter(user =>
        user.name.toLowerCase().includes(searchLower) ||
        user.email.toLowerCase().includes(searchLower) ||
        user.phone?.toLowerCase().includes(searchLower)
      );
    }

    if (filters.role) {
      filteredUsers = filteredUsers.filter(user => user.role === filters.role);
    }

    if (filters.status) {
      filteredUsers = filteredUsers.filter(user => user.status === filters.status);
    }

    if (filters.department) {
      filteredUsers = filteredUsers.filter(user => user.department === filters.department);
    }
  }

  const total = filteredUsers.length;

  // 应用分页
  if (pagination) {
    const startIndex = (pagination.page - 1) * pagination.limit;
    const endIndex = startIndex + pagination.limit;
    filteredUsers = filteredUsers.slice(startIndex, endIndex);
  }

  return {
    users: filteredUsers,
    total,
    page: pagination?.page || 1,
    limit: pagination?.limit || total,
    totalPages: pagination ? Math.ceil(total / pagination.limit) : 1,
  };
}

// 根据ID获取用户
export function getUserById(id: string): User | null {
  return users.find(user => user.id === id) || null;
}

// 创建用户
export function createUser(userData: CreateUserData): User {
  // 检查邮箱是否已存在
  const existingUser = users.find(user => user.email === userData.email);
  if (existingUser) {
    throw new Error('邮箱已存在');
  }

  const now = new Date().toISOString();
  const newUser: User = {
    id: generateId(),
    ...userData,
    createdAt: now,
    updatedAt: now,
  };

  users.push(newUser);
  return newUser;
}

// 更新用户
export function updateUser(userData: UpdateUserData): User {
  const userIndex = users.findIndex(user => user.id === userData.id);
  if (userIndex === -1) {
    throw new Error('用户不存在');
  }

  // 检查邮箱是否被其他用户使用
  if (userData.email) {
    const existingUser = users.find(user => user.email === userData.email && user.id !== userData.id);
    if (existingUser) {
      throw new Error('邮箱已被其他用户使用');
    }
  }

  const updatedUser: User = {
    ...users[userIndex],
    ...userData,
    updatedAt: new Date().toISOString(),
  };

  users[userIndex] = updatedUser;
  return updatedUser;
}

// 删除用户
export function deleteUser(id: string): boolean {
  const userIndex = users.findIndex(user => user.id === id);
  if (userIndex === -1) {
    return false;
  }

  users.splice(userIndex, 1);
  return true;
}

// 获取统计信息
export function getUserStats() {
  const total = users.length;
  const active = users.filter(user => user.status === 'active').length;
  const inactive = users.filter(user => user.status === 'inactive').length;
  const pending = users.filter(user => user.status === 'pending').length;

  const roleStats = {
    admin: users.filter(user => user.role === 'admin').length,
    user: users.filter(user => user.role === 'user').length,
    moderator: users.filter(user => user.role === 'moderator').length,
  };

  return {
    total,
    active,
    inactive,
    pending,
    roleStats,
  };
}
